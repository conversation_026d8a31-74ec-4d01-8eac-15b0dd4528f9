export interface RpaStepBase {
    id: string;
    type: string;
    description?: string;
    timeout?: number;
}
export interface NavigateStep extends RpaStepBase {
    type: 'navigate';
    url: string;
    waitUntil?: 'load' | 'domcontentloaded' | 'networkidle';
}
export interface GoBackStep extends RpaStepBase {
    type: 'goBack';
}
export interface GoForwardStep extends RpaStepBase {
    type: 'goForward';
}
export interface ReloadStep extends RpaStepBase {
    type: 'reload';
}
export interface ClickStep extends RpaStepBase {
    type: 'click';
    selector: string;
    button?: 'left' | 'right' | 'middle';
    clickCount?: number;
    force?: boolean;
}
export interface FillStep extends RpaStepBase {
    type: 'fill';
    selector: string;
    value: string;
    force?: boolean;
}
export interface TypeStep extends RpaStepBase {
    type: 'type';
    selector: string;
    text: string;
    delay?: number;
}
export interface SelectOptionStep extends RpaStepBase {
    type: 'selectOption';
    selector: string;
    value?: string;
    label?: string;
    index?: number;
}
export interface CheckStep extends RpaStepBase {
    type: 'check';
    selector: string;
    force?: boolean;
}
export interface UncheckStep extends RpaStepBase {
    type: 'uncheck';
    selector: string;
    force?: boolean;
}
export interface WaitForSelectorStep extends RpaStepBase {
    type: 'waitForSelector';
    selector: string;
    state?: 'attached' | 'detached' | 'visible' | 'hidden';
}
export interface WaitForTimeoutStep extends RpaStepBase {
    type: 'waitForTimeout';
    duration: number;
}
export interface WaitForUrlStep extends RpaStepBase {
    type: 'waitForUrl';
    url: string | RegExp;
}
export interface ExtractTextStep extends RpaStepBase {
    type: 'extractText';
    selector: string;
    variableName: string;
}
export interface ExtractAttributeStep extends RpaStepBase {
    type: 'extractAttribute';
    selector: string;
    attribute: string;
    variableName: string;
}
export interface TakeScreenshotStep extends RpaStepBase {
    type: 'takeScreenshot';
    path?: string;
    fullPage?: boolean;
    variableName?: string;
}
export interface IfElementExistsStep extends RpaStepBase {
    type: 'ifElementExists';
    selector: string;
    thenSteps: RpaStep[];
    elseSteps?: RpaStep[];
}
export interface ConditionalClickStep extends RpaStepBase {
    type: 'conditionalClick';
    selector: string;
    condition: 'exists' | 'enabled' | 'disabled';
    clickIfTrue?: boolean;
    button?: 'left' | 'right' | 'middle';
    force?: boolean;
}
export interface FillPasswordStep extends RpaStepBase {
    type: 'fillPassword';
    selector: string;
    credentialId: string;
    force?: boolean;
}
export interface Fill2FAStep extends RpaStepBase {
    type: 'fill2FA';
    selector: string;
    credentialId: string;
    force?: boolean;
}
export interface DownloadFileStep extends RpaStepBase {
    type: 'downloadFile';
    triggerSelector?: string;
    filename?: string;
    variableName?: string;
    saveToFile?: boolean;
}
export type RpaStep = NavigateStep | GoBackStep | GoForwardStep | ReloadStep | ClickStep | FillStep | TypeStep | SelectOptionStep | CheckStep | UncheckStep | WaitForSelectorStep | WaitForTimeoutStep | WaitForUrlStep | ExtractTextStep | ExtractAttributeStep | TakeScreenshotStep | IfElementExistsStep | ConditionalClickStep | FillPasswordStep | Fill2FAStep | DownloadFileStep;
export interface RpaFlow {
    id: string;
    name: string;
    description?: string;
    customerId: string;
    steps: RpaStep[];
    variables?: Record<string, any>;
    settings?: FlowSettings;
    createdAt: Date;
    updatedAt: Date;
}
export interface FlowSettings {
    browser?: 'chromium' | 'firefox' | 'webkit';
    headless?: boolean;
    viewport?: {
        width: number;
        height: number;
    };
    userAgent?: string;
    locale?: string;
    timezone?: string;
}
export interface CredentialBase {
    id: string;
    name: string;
    description?: string;
    type: 'password' | '2fa';
    createdAt: Date;
    updatedAt: Date;
}
export interface PasswordCredential extends CredentialBase {
    type: 'password';
    username: string;
}
export interface TwoFactorCredential extends CredentialBase {
    type: '2fa';
}
export type Credential = PasswordCredential | TwoFactorCredential;
export interface CreatePasswordCredentialRequest {
    name: string;
    description?: string;
    username: string;
    password: string;
}
export interface CreateTwoFactorCredentialRequest {
    name: string;
    description?: string;
    secret: string;
}
export type CreateCredentialRequest = CreatePasswordCredentialRequest | CreateTwoFactorCredentialRequest;
export interface UpdatePasswordCredentialRequest {
    name?: string;
    description?: string;
    username?: string;
    password?: string;
}
export interface UpdateTwoFactorCredentialRequest {
    name?: string;
    description?: string;
    secret?: string;
}
export interface Customer {
    id: string;
    customerNumber: string;
    name: string;
    vismaNumber?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface CustomerToken {
    id: string;
    customerId: string;
    name: string;
    description?: string;
    apiToken?: string;
    refreshToken?: string;
    hasApiToken?: boolean;
    hasRefreshToken?: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface CreateCustomerRequest {
    customerNumber: string;
    name: string;
    vismaNumber?: string;
}
export interface UpdateCustomerRequest {
    customerNumber?: string;
    name?: string;
    vismaNumber?: string;
}
export interface CreateCustomerTokenRequest {
    name: string;
    description?: string;
    apiToken?: string;
    refreshToken?: string;
}
export interface UpdateCustomerTokenRequest {
    name?: string;
    description?: string;
    apiToken?: string;
    refreshToken?: string;
}
export type UpdateCredentialRequest = UpdatePasswordCredentialRequest | UpdateTwoFactorCredentialRequest;
export interface FlowExecution {
    id: string;
    flowId: string;
    status: ExecutionStatus;
    startedAt: Date;
    completedAt?: Date;
    error?: string;
    logs: ExecutionLog[];
    results?: Record<string, any>;
}
export type ExecutionStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export interface ExecutionLog {
    timestamp: Date;
    level: 'info' | 'warn' | 'error' | 'debug';
    message: string;
    stepId?: string;
    data?: any;
}
export interface CreateFlowRequest {
    name: string;
    description?: string;
    customerId: string;
    steps: RpaStep[];
    settings?: FlowSettings;
}
export interface UpdateFlowRequest {
    name?: string;
    description?: string;
    customerId?: string;
    steps?: RpaStep[];
    settings?: FlowSettings;
}
export interface ExecuteFlowRequest {
    flowId: string;
    variables?: Record<string, any>;
}
export interface FlowSchedule {
    id: string;
    flowId: string;
    name: string;
    description?: string;
    cronExpression: string;
    timezone: string;
    enabled: boolean;
    variables?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
    lastRunAt?: Date;
    nextRunAt?: Date;
}
export interface CreateScheduleRequest {
    flowId: string;
    name: string;
    description?: string;
    cronExpression: string;
    timezone?: string;
    enabled?: boolean;
    variables?: Record<string, any>;
}
export interface UpdateScheduleRequest {
    name?: string;
    description?: string;
    cronExpression?: string;
    timezone?: string;
    enabled?: boolean;
    variables?: Record<string, any>;
}
export interface ScheduleQuery {
    flowId?: string;
    enabled?: boolean;
    limit?: number;
    offset?: number;
}
export interface CronExpressionInfo {
    expression: string;
    description: string;
    nextRuns: Date[];
    isValid: boolean;
    error?: string;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface FlowNode {
    id: string;
    type: string;
    position: {
        x: number;
        y: number;
    };
    data: {
        step: RpaStep;
        label: string;
    };
}
export interface FlowEdge {
    id: string;
    source: string;
    target: string;
    type?: string;
}
export interface FlowData {
    nodes: FlowNode[];
    edges: FlowEdge[];
}
export interface ValidationError {
    field: string;
    message: string;
    code: string;
}
export interface ValidationResult {
    valid: boolean;
    errors: ValidationError[];
}
export declare function generateId(): string;
export declare function createEmptySchedule(flowId: string, name: string): FlowSchedule;
export declare const COMMON_CRON_EXPRESSIONS: {
    readonly 'Every minute': "* * * * *";
    readonly 'Every 5 minutes': "*/5 * * * *";
    readonly 'Every 15 minutes': "*/15 * * * *";
    readonly 'Every 30 minutes': "*/30 * * * *";
    readonly 'Every hour': "0 * * * *";
    readonly 'Every 2 hours': "0 */2 * * *";
    readonly 'Every 6 hours': "0 */6 * * *";
    readonly 'Every 12 hours': "0 */12 * * *";
    readonly 'Daily at 9 AM': "0 9 * * *";
    readonly 'Daily at 6 PM': "0 18 * * *";
    readonly 'Weekdays at 9 AM': "0 9 * * 1-5";
    readonly 'Weekends at 10 AM': "0 10 * * 6,0";
    readonly 'Weekly on Monday at 9 AM': "0 9 * * 1";
    readonly 'Monthly on 1st at 9 AM': "0 9 1 * *";
    readonly 'Yearly on Jan 1st at 9 AM': "0 9 1 1 *";
};
export declare function getCronDescription(cronExpression: string): string;
export declare function createEmptyFlow(name: string, customerId?: string): RpaFlow;
export declare function createStepFromType(stepType: string): RpaStep;
export declare function getStepLabel(step: RpaStep): string;
export declare function convertFlowToReactFlow(flow: RpaFlow): FlowData;
export declare function convertReactFlowToFlow(flowData: FlowData, flowId: string, flowName: string, customerId?: string): RpaFlow;
//# sourceMappingURL=types.d.ts.map