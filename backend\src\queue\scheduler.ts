import { Queue, Job } from 'bullmq';
import { ScheduleService } from '../services/scheduleService';
import { QueueService } from './queueService';
import { ExecutionService } from '../services/executionService';
import { FlowSchedule } from '@rpa-project/shared';
import { CronExpressionParser } from 'cron-parser';

const SCHEDULER_QUEUE_NAME = process.env.SCHEDULER_QUEUE_NAME || 'rpa-scheduler';
const REDIS_HOST = process.env.REDIS_HOST || 'localhost';
const REDIS_PORT = parseInt(process.env.REDIS_PORT || '6379');
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || undefined;

// Redis connection configuration
const redisConfig = {
  host: REDIS_HOST,
  port: REDIS_PORT,
  password: REDIS_PASSWORD,
  maxRetriesPerRequest: null,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
};

export interface ScheduledJobData {
  scheduleId: string;
  flowId: string;
  variables?: Record<string, any>;
}

export class FlowScheduler {
  private schedulerQueue: Queue;
  private scheduleService: ScheduleService;
  private queueService: QueueService;
  private executionService: ExecutionService;
  private isRunning = false;
  private checkInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.schedulerQueue = new Queue(SCHEDULER_QUEUE_NAME, {
      connection: redisConfig,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.scheduleService = new ScheduleService();
    this.queueService = new QueueService();
    this.executionService = new ExecutionService();
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('📅 Scheduler is already running');
      return;
    }

    console.log('📅 Starting flow scheduler...');
    this.isRunning = true;

    // Check for due schedules every minute
    this.checkInterval = setInterval(async () => {
      await this.checkAndScheduleDueFlows();
    }, 60000); // 60 seconds

    // Initial check
    await this.checkAndScheduleDueFlows();
    
    console.log('✅ Flow scheduler started');
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('📅 Scheduler is not running');
      return;
    }

    console.log('📅 Stopping flow scheduler...');
    this.isRunning = false;

    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    await this.schedulerQueue.close();
    console.log('✅ Flow scheduler stopped');
  }

  async addSchedule(schedule: FlowSchedule): Promise<void> {
    if (!schedule.enabled || !schedule.nextRunAt) {
      console.log(`📅 Skipping schedule ${schedule.name}: enabled=${schedule.enabled}, nextRunAt=${schedule.nextRunAt}`);
      return;
    }

    const delay = schedule.nextRunAt.getTime() - Date.now();
    if (delay <= 0) {
      // Schedule is due now, execute immediately
      console.log(`📅 Schedule ${schedule.name} is due now, executing immediately`);
      await this.executeScheduledFlow(schedule);
      return;
    }

    // For this implementation, we rely on polling rather than BullMQ delayed jobs
    // The polling mechanism will pick up due schedules every minute
    console.log(`📅 Schedule ${schedule.name} added - next run at ${schedule.nextRunAt.toISOString()} (in ${Math.round(delay / 1000 / 60)} minutes)`);
  }

  async removeSchedule(scheduleId: string): Promise<void> {
    // Since we're using polling, no need to remove from queue
    console.log(`📅 Schedule ${scheduleId} removed from scheduler`);
  }

  async updateSchedule(schedule: FlowSchedule): Promise<void> {
    // Since we're using polling, just log the update
    console.log(`📅 Schedule ${schedule.name} updated - enabled: ${schedule.enabled}, next run: ${schedule.nextRunAt?.toISOString()}`);
  }

  private async checkAndScheduleDueFlows(): Promise<void> {
    try {
      const currentTime = new Date();
      console.log(`📅 Checking for due schedules at ${currentTime.toISOString()}`);

      const dueSchedules = await this.scheduleService.getSchedulesDueForExecution(currentTime);
      console.log(`📅 Found ${dueSchedules.length} due schedules`);

      for (const schedule of dueSchedules) {
        console.log(`📅 Processing due schedule: ${schedule.name} (next run: ${schedule.nextRunAt?.toISOString()})`);
        await this.executeScheduledFlow(schedule);
      }
    } catch (error) {
      console.error('Error checking due schedules:', error);
    }
  }

  private async executeScheduledFlow(schedule: FlowSchedule): Promise<void> {
    try {
      console.log(`📅 Executing scheduled flow: ${schedule.name} (${schedule.flowId})`);

      // Create execution record first (like the normal API flow)
      const execution = await this.executionService.createExecution(
        schedule.flowId,
        schedule.variables || {}
      );

      console.log(`📅 Created execution record: ${execution.id}`);

      // Add to queue for processing
      await this.queueService.addFlowExecution(execution.id, {
        flowId: schedule.flowId,
        variables: schedule.variables || {}
      });

      // Calculate next run time
      const nextRunAt = this.calculateNextRunTime(schedule.cronExpression, schedule.timezone);

      // Update schedule with last run and next run times
      await this.scheduleService.updateScheduleLastRun(
        schedule.id,
        new Date(),
        nextRunAt
      );

      // Schedule next execution if there is a next run time
      if (nextRunAt && schedule.enabled) {
        const updatedSchedule = { ...schedule, lastRunAt: new Date(), nextRunAt };
        await this.addSchedule(updatedSchedule);
      }

      console.log(`✅ Scheduled flow executed successfully: ${execution.id}`);
    } catch (error) {
      console.error(`❌ Error executing scheduled flow ${schedule.id}:`, error);
    }
  }

  private calculateNextRunTime(cronExpression: string, timezone: string = 'UTC'): Date | undefined {
    try {
      const interval = CronExpressionParser.parse(cronExpression, {
        tz: timezone,
        currentDate: new Date()
      });
      return interval.next().toDate();
    } catch (error) {
      console.error('Error calculating next run time:', error);
      return undefined;
    }
  }

  async initializeExistingSchedules(): Promise<void> {
    try {
      console.log('📅 Initializing existing schedules...');
      
      const enabledSchedules = await this.scheduleService.getEnabledSchedules();
      
      for (const schedule of enabledSchedules) {
        // Recalculate next run time if not set or in the past
        if (!schedule.nextRunAt || schedule.nextRunAt <= new Date()) {
          const nextRunAt = this.calculateNextRunTime(schedule.cronExpression, schedule.timezone);
          if (nextRunAt) {
            await this.scheduleService.updateScheduleLastRun(schedule.id, new Date(), nextRunAt);
            schedule.nextRunAt = nextRunAt;
          }
        }
        
        if (schedule.nextRunAt) {
          await this.addSchedule(schedule);
        }
      }
      
      console.log(`✅ Initialized ${enabledSchedules.length} existing schedules`);
    } catch (error) {
      console.error('Error initializing existing schedules:', error);
    }
  }

  async getSchedulerStats(): Promise<any> {
    const waiting = await this.schedulerQueue.getWaiting();
    const active = await this.schedulerQueue.getActive();
    const completed = await this.schedulerQueue.getCompleted();
    const failed = await this.schedulerQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      isRunning: this.isRunning
    };
  }
}

// Global scheduler instance
let globalScheduler: FlowScheduler | null = null;

export function getScheduler(): FlowScheduler {
  if (!globalScheduler) {
    globalScheduler = new FlowScheduler();
  }
  return globalScheduler;
}

export async function initializeScheduler(): Promise<void> {
  const scheduler = getScheduler();
  await scheduler.start();
  await scheduler.initializeExistingSchedules();
}

export async function shutdownScheduler(): Promise<void> {
  if (globalScheduler) {
    await globalScheduler.stop();
    globalScheduler = null;
  }
}
