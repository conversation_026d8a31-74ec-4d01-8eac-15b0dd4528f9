# RPA Runner System - Multi-Engine Execution Architecture

## Overview

The RPA Runner System provides a flexible, extensible architecture for executing RPA flows using different execution engines based on step types. This system replaces the previous hardcoded PlaywrightRunner approach with a modular design that supports multiple specialized runners.

## Architecture Components

### 1. Core Interfaces (`IRunner.ts`)

- **`IRunner`**: Abstract interface defining the contract for all execution engines
- **`BaseRunner`**: Abstract base class providing common functionality
- **`RunnerContext`**: Context interface for step execution
- **`StepExecutionResult`**: Result interface for step execution

### 2. Step Type Categorization (`stepTypes.ts`)

- **Step Categories**: Web automation, API, Database, File system
- **Runner Types**: Playwright, API, Database, File system
- **Mapping Functions**: Determine which runner handles which step types
- **Support Detection**: Check if step types and runners are implemented

### 3. Runner Registry (`RunnerRegistry.ts`)

- **Registration**: Map runner types to implementation classes
- **Instance Management**: Create and cache runner instances per execution
- **Cleanup**: Manage resource cleanup for executions

### 4. Runner Factory (`RunnerFactory.ts`)

- **Creation**: Instantiate appropriate runners based on step types
- **Configuration**: Set up logging and cancellation handlers
- **Analysis**: Analyze flows to determine required runners
- **Validation**: Check flow compatibility with available runners

### 5. Flow Executor (`FlowExecutor.ts`)

- **Orchestration**: Coordinate execution across multiple runners
- **Step Routing**: Route steps to appropriate runners
- **Context Management**: Maintain execution context and variables
- **Error Handling**: Handle failures and cleanup resources

### 6. PlaywrightRunner (`playwrightRunner.ts`)

- **Refactored**: Now implements `IRunner` interface
- **Web Automation**: Handles all browser-based automation steps
- **Backward Compatible**: Maintains existing functionality

## Current Implementation Status

### ✅ Implemented Runners
- **PlaywrightRunner**: Handles all web automation steps
  - Navigation: `navigate`, `goBack`, `goForward`, `reload`
  - Interaction: `click`, `fill`, `type`, `selectOption`, `check`, `uncheck`
  - Waiting: `waitForSelector`, `waitForTimeout`, `waitForUrl`
  - Data Extraction: `extractText`, `extractAttribute`, `takeScreenshot`
  - Conditional: `ifElementExists`, `conditionalClick`
  - Security: `fillPassword`, `fill2FA`

### 🚧 Future Runners (Architecture Ready)
- **API Runner**: HTTP requests, REST calls, GraphQL queries
- **Database Runner**: SQL operations, stored procedures
- **File System Runner**: File operations, directory management

## Usage Examples

### Basic Flow Execution
```typescript
import { FlowExecutor } from './runner/FlowExecutor';

const executor = new FlowExecutor(
  executionId,
  logHandler,
  cancellationChecker
);

const results = await executor.executeFlow(flow, variables);
```

### Runner Factory Usage
```typescript
import { getRunnerFactory } from './runner/RunnerFactory';

const factory = getRunnerFactory();

// Analyze flow compatibility
const analysis = factory.analyzeFlow(flow);
console.log('Required runners:', analysis.requiredRunners);
console.log('Unsupported steps:', analysis.unsupportedSteps);

// Create specific runner
const runner = factory.createRunnerForStep('navigate', {
  logHandler: myLogHandler,
  cancellationChecker: myCancellationChecker
});
```

### Adding New Runners
```typescript
import { RunnerRegistry, RunnerType } from './runner';

// 1. Implement IRunner interface
class ApiRunner extends BaseRunner {
  getSupportedStepTypes(): string[] {
    return ['httpGet', 'httpPost', 'restCall'];
  }
  
  async executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult> {
    // Implementation
  }
  
  // ... other required methods
}

// 2. Register the runner
const registry = getRunnerRegistry();
registry.registerRunner(RunnerType.API, ApiRunner);

// 3. Update stepTypes.ts to mark as implemented
```

## Key Benefits

### 1. **Extensibility**
- Easy to add new runner types for different automation domains
- Clean separation of concerns between different execution engines
- Modular architecture supports independent development of runners

### 2. **Flexibility**
- Flows can mix different types of steps (web, API, database, etc.)
- Automatic routing of steps to appropriate runners
- Runtime analysis of flow requirements

### 3. **Maintainability**
- Clear interfaces and contracts
- Centralized runner management
- Consistent error handling and logging

### 4. **Performance**
- Runner instance caching per execution
- Efficient resource management
- Parallel initialization of multiple runners

### 5. **Backward Compatibility**
- Existing web automation flows work unchanged
- Same API for flow execution
- Gradual migration path for new features

## Migration Notes

### What Changed
- `FlowExecutionWorker` now uses `FlowExecutor` instead of direct `PlaywrightRunner`
- `PlaywrightRunner` implements `IRunner` interface
- Step execution routing through runner factory
- Enhanced error handling and logging

### What Stayed the Same
- All existing step types and their functionality
- Flow definition structure
- Execution API and results format
- Database schema and storage

### Breaking Changes
- None for existing flows and API consumers
- Internal architecture changes only

## Future Roadmap

### Phase 1: API Runner
- HTTP request steps (`httpGet`, `httpPost`, etc.)
- REST API integration
- Authentication handling

### Phase 2: Database Runner
- SQL query execution
- Database connection management
- Transaction support

### Phase 3: File System Runner
- File operations
- Directory management
- Archive handling

### Phase 4: Custom Integrations
- Service-specific runners
- Plugin architecture
- External runner registration
