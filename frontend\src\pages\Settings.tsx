import { useState } from 'react'

export function Settings() {
  const [masterKey, setMasterKey] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const handleUpdateMasterKey = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!masterKey.trim()) {
      setMessage({ type: 'error', text: 'Huvu<PERSON>yckel kan inte vara tom' })
      return
    }

    if (masterKey.length !== 64) {
      setMessage({ type: 'error', text: 'Huvu<PERSON>yckel måste vara exakt 64 hex-tecken (32 bytes)' })
      return
    }

    if (!/^[0-9a-fA-F]+$/.test(masterKey)) {
      setMessage({ type: 'error', text: 'Huvudnyckel får endast innehålla hexadecimala tecken (0-9, a-f, A-F)' })
      return
    }

    try {
      setLoading(true)
      setMessage(null)

      const response = await fetch('/api/settings/master-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ masterKey })
      })

      const result = await response.json()

      if (result.success) {
        setMessage({ type: 'success', text: 'Huvudnyckel uppdaterad framgångsrikt' })
        setMasterKey('')
      } else {
        setMessage({ type: 'error', text: result.error || 'Misslyckades att uppdatera huvudnyckel' })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Misslyckades att uppdatera huvudnyckel' })
      console.error('Error updating master key:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateRandomKey = () => {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
    setMasterKey(hex.toUpperCase())
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Inställningar</p>
          <p className="dashboard-subtitle">
            Konfigurera applikationsinställningar och säkerhet.
          </p>
        </div>
      </div>

      {/* Master Key Section */}
      <h2 className="section-title">Huvudnyckel-konfiguration</h2>
      <div className="table-container">
        <div className="activity-table">
          <div style={{ padding: '1.5rem' }}>
            <p style={{ margin: '0 0 1.5rem 0', color: '#6b7280', fontSize: '0.875rem' }}>
              Huvudnyckeln används för att kryptera och dekryptera lagrade inloggningsuppgifter.
              Den måste vara exakt 32 bytes (64 hexadecimala tecken).
            </p>

            {/* Warning */}
            <div style={{
              padding: '1rem',
              backgroundColor: '#fef3c7',
              border: '1px solid #f59e0b',
              borderRadius: '0.375rem',
              marginBottom: '1.5rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                <span style={{ fontSize: '1.25rem' }}>⚠️</span>
                <strong style={{ color: '#92400e' }}>Viktigt</strong>
              </div>
              <p style={{ margin: 0, color: '#92400e', fontSize: '0.875rem' }}>
                Att ändra huvudnyckeln kommer att göra alla befintliga inloggningsuppgifter otillgängliga.
                Ändra endast detta om du vet vad du gör.
              </p>
            </div>

            {/* Message */}
            {message && (
              <div style={{
                padding: '0.75rem',
                backgroundColor: message.type === 'success' ? '#f0fdf4' : '#fef2f2',
                border: `1px solid ${message.type === 'success' ? '#bbf7d0' : '#fecaca'}`,
                borderRadius: '0.375rem',
                color: message.type === 'success' ? '#166534' : '#dc2626',
                marginBottom: '1.5rem',
                fontSize: '0.875rem'
              }}>
                {message.text}
              </div>
            )}

            <form onSubmit={handleUpdateMasterKey}>
              <div className="form-group">
                <label className="form-label">
                  Huvudnyckel (64 hex-tecken)
                </label>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <input
                    type="text"
                    value={masterKey}
                    onChange={(e) => setMasterKey(e.target.value.toUpperCase())}
                    className="form-input"
                    style={{ flex: 1, fontFamily: 'monospace' }}
                    placeholder="Ange 64 hex-tecken (t.ex. A1B2C3D4...)"
                    maxLength={64}
                  />
                  <button
                    type="button"
                    onClick={generateRandomKey}
                    className="action-button secondary"
                  >
                    <span>Generera</span>
                  </button>
                </div>
                <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  {masterKey.length}/64 tecken
                </div>
              </div>

              <button
                type="submit"
                disabled={loading || masterKey.length !== 64}
                className={`action-button ${loading || masterKey.length !== 64 ? 'secondary' : 'danger'}`}
                style={{ cursor: loading || masterKey.length !== 64 ? 'not-allowed' : 'pointer' }}
              >
                <span>{loading ? 'Uppdaterar...' : 'Uppdatera huvudnyckel'}</span>
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Other Settings */}
      <h2 className="section-title">Applikationsinställningar</h2>
      <div className="table-container">
        <div className="activity-table">
          <div style={{ padding: '1.5rem' }}>
            <p style={{ margin: 0, color: '#6b7280', fontSize: '0.875rem' }}>
              Ytterligare applikationsinställningar kommer att finnas tillgängliga här i framtida uppdateringar.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
