const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testMultipleExecutions() {
  console.log('🧪 Testing Multiple Flow Executions...\n');

  try {
    // Create a test flow
    console.log('1. Creating test flow...');
    const testFlow = {
      name: 'Multi-Execution Test Flow',
      description: 'Testing multiple executions of the same flow',
      steps: [
        {
          id: 'step1',
          type: 'navigate',
          url: 'https://httpbin.org/delay/1',
          waitUntil: 'load',
          timeout: 30000,
          description: 'Navigate to httpbin delay endpoint'
        }
      ],
      settings: {
        browserType: 'chromium',
        headless: true,
        viewportWidth: 1280,
        viewportHeight: 720
      }
    };

    const createResponse = await axios.post(`${API_BASE}/flows`, testFlow);
    const flowId = createResponse.data.data.id;
    console.log(`✅ Flow created with ID: ${flowId}\n`);

    // Execute the flow multiple times
    const executions = [];
    console.log('2. Starting multiple executions...');
    
    for (let i = 1; i <= 3; i++) {
      console.log(`   Starting execution ${i}...`);
      const executeResponse = await axios.post(`${API_BASE}/executions`, {
        flowId: flowId,
        variables: { executionNumber: i }
      });
      executions.push({
        id: executeResponse.data.data.id,
        number: i
      });
      console.log(`   ✅ Execution ${i} started: ${executeResponse.data.data.id}`);
      
      // Small delay between executions
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('\n3. Waiting for executions to complete...');
    await new Promise(resolve => setTimeout(resolve, 8000));

    // Check status of all executions
    console.log('\n4. Checking execution statuses...');
    for (const execution of executions) {
      try {
        const statusResponse = await axios.get(`${API_BASE}/executions/${execution.id}`);
        const status = statusResponse.data.data.status;
        console.log(`   Execution ${execution.number} (${execution.id}): ${status}`);
        
        if (status === 'failed' && statusResponse.data.data.error) {
          console.log(`     Error: ${statusResponse.data.data.error}`);
        }
      } catch (error) {
        console.log(`   Execution ${execution.number} (${execution.id}): ERROR - ${error.message}`);
      }
    }

    // List all executions for this flow
    console.log('\n5. Listing all executions for this flow...');
    const allExecutionsResponse = await axios.get(`${API_BASE}/executions?flowId=${flowId}`);
    console.log(`   Found ${allExecutionsResponse.data.data.length} executions for flow ${flowId}`);

    console.log('\n🎉 Multiple execution test completed!');
    console.log('If you see "completed" status for all executions, the persistence fix is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data?.error) {
      console.error('Error details:', error.response.data.error);
    }
  }
}

// Run the test
testMultipleExecutions();
