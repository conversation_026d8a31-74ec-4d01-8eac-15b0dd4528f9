# Use Node.js 18 with Playwright dependencies
FROM mcr.microsoft.com/playwright:v1.40.0-focal

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY shared/package*.json ./shared/

# Install dependencies
RUN npm ci --only=production

# Copy shared types
COPY shared/ ./shared/

# Build shared package
WORKDIR /app/shared
RUN npm run build

# Copy backend source
WORKDIR /app
COPY backend/ ./backend/

# Build backend
WORKDIR /app/backend
RUN npm run build

# Create directories for screenshots and downloads
RUN mkdir -p /app/screenshots /app/downloads

# Install Playwright browsers
RUN npx playwright install chromium firefox webkit
RUN npx playwright install-deps

# Expose port
EXPOSE 3001

# Set environment variables
ENV NODE_ENV=production
ENV REDIS_URL=redis://redis:6379

# Start the application
CMD ["npm", "start"]
