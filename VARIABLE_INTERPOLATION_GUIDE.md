# Variabel-Interpolation Guide

## Översikt

RPA-systemet stöder nu variabel-interpolation som låter dig extrahera data från webbsidor och använda den i efterföljande steg. Detta gör dina flöden dynamiska och kraftfulla.

## Hur det fungerar

### 1. Extrahera Data

Använd `extractText` eller `extractAttribute` steg för att hämta data från webbsidor:

```json
{
  "type": "extractText",
  "selector": "h1",
  "variableName": "pageTitle",
  "description": "Extrahera sidans titel"
}
```

```json
{
  "type": "extractAttribute", 
  "selector": "form",
  "attribute": "action",
  "variableName": "formAction",
  "description": "Extrahera formulärets action-URL"
}
```

### 2. Använda Extraherad Data

Referera till variabler med `${variableName}` syntax i alla efterföljande steg:

```json
{
  "type": "fill",
  "selector": "input[name='title']",
  "value": "Titel: ${pageTitle}",
  "description": "Fyll i titel med extraherad data"
}
```

```json
{
  "type": "navigate",
  "url": "${formAction}",
  "description": "Navigera till formulärets action-URL"
}
```

## Steg som stöder variabler

### Navigation
- **navigate**: URL kan innehålla variabler
- **waitForUrl**: URL kan innehålla variabler

### Interaktion
- **fill**: Både selector och värde stöder variabler
- **type**: Både selector och text stöder variabler
- **click**: Selector stöder variabler
- **selectOption**: Selector, värde och label stöder variabler

### Extraktion
- **extractText**: Selector stöder variabler
- **extractAttribute**: Selector och attribut stöder variabler
- **takeScreenshot**: Filsökväg stöder variabler

### Villkorliga steg
- **ifElementExists**: Selector stöder variabler
- **conditionalClick**: Selector stöder variabler

## Exempel på användning

### Grundläggande exempel
```json
{
  "steps": [
    {
      "type": "navigate",
      "url": "https://example.com"
    },
    {
      "type": "extractText",
      "selector": ".product-name",
      "variableName": "productName"
    },
    {
      "type": "fill",
      "selector": "#search",
      "value": "Sök efter: ${productName}"
    }
  ]
}
```

### Avancerat exempel med flera variabler
```json
{
  "steps": [
    {
      "type": "extractText",
      "selector": ".customer-name",
      "variableName": "customerName"
    },
    {
      "type": "extractAttribute",
      "selector": ".customer-id",
      "attribute": "data-id",
      "variableName": "customerId"
    },
    {
      "type": "takeScreenshot",
      "path": "customer-${customerId}-${customerName}.png"
    },
    {
      "type": "navigate",
      "url": "https://api.example.com/customers/${customerId}"
    }
  ]
}
```

## Frontend UI-stöd

### Variabel-knapp
- Klicka på 📝 knappen bredvid input-fält för att se tillgängliga variabler
- Klicka på en variabel för att infoga den automatiskt

### Validering
- Röd ram visas om du refererar till variabler som inte finns
- Varningsmeddelanden visar vilka variabler som saknas

### Tips
- Variabler är endast tillgängliga från tidigare steg i flödet
- Använd beskrivande variabelnamn för bättre läsbarhet
- Variabler som inte finns lämnas orörda istället för att krascha flödet

## Testning

Använd test-filen för att verifiera att systemet fungerar:

```bash
node test-variable-interpolation.js
```

Detta kommer att:
1. Skapa ett testflöde med variabel-interpolation
2. Köra flödet och övervaka resultatet
3. Verifiera att variabler extraheras och används korrekt

## Felsökning

### Variabler ersätts inte
- Kontrollera att variabelnamnet är korrekt stavat
- Se till att extract-steget kommer före användning
- Kolla execution logs för fel

### Frontend visar fel
- Kontrollera att shared paketet är kompilerat
- Verifiera att alla imports fungerar
- Kolla browser console för JavaScript-fel

### Backend-fel
- Se till att interpolateVariables funktionen importeras korrekt
- Kontrollera att alla steg-typer har uppdaterats för variabel-stöd
