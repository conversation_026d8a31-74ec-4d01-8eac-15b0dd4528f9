import { chromium, firefox, webkit, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext } from 'playwright';
import {
  RpaFlow,
  RpaStep,
  FlowSettings,
  ExecutionLog
} from '@rpa-project/shared';
import { credentialService } from '../services/credentialService';
import { BaseRunner, RunnerContext, StepExecutionResult } from './IRunner';
import { WEB_AUTOMATION_STEPS } from './stepTypes';
import fs from 'fs';
import path from 'path';

// Local implementation of interpolateVariables until shared package is fixed
function interpolateVariables(text: string, variables: Record<string, any>): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Replace ${variableName} with actual values
  return text.replace(/\$\{([^}]+)\}/g, (match, variableName) => {
    const trimmedName = variableName.trim();

    if (trimmedName in variables) {
      const value = variables[trimmedName];
      // Convert to string, handling null/undefined
      return value != null ? String(value) : '';
    }

    // If variable not found, leave the placeholder as is
    return match;
  });
}

export interface PlaywrightRunnerContext {
  browser: Browser;
  context: BrowserContext;
  page: Page;
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
}

export class PlaywrightRunner extends BaseRunner {
  private playwrightContext?: PlaywrightRunnerContext;

  async initialize(settings: FlowSettings, variables: Record<string, any> = {}): Promise<void> {
    const browserType = settings.browser || 'chromium';
    const headless = settings.headless !== false;

    let browser: Browser;
    switch (browserType) {
      case 'firefox':
        browser = await firefox.launch({ headless });
        break;
      case 'webkit':
        browser = await webkit.launch({ headless });
        break;
      default:
        browser = await chromium.launch({ headless });
    }

    const contextOptions: any = {};
    
    if (settings.viewport) {
      contextOptions.viewport = settings.viewport;
    }
    
    if (settings.userAgent) {
      contextOptions.userAgent = settings.userAgent;
    }
    
    if (settings.locale) {
      contextOptions.locale = settings.locale;
    }
    
    if (settings.timezone) {
      contextOptions.timezoneId = settings.timezone;
    }

    const context = await browser.newContext(contextOptions);
    const page = await context.newPage();

    this.playwrightContext = {
      browser,
      context,
      page,
      variables: { ...variables },
      onLog: this.logHandler // Use the stored log handler
    };
  }

  getSupportedStepTypes(): string[] {
    return [...WEB_AUTOMATION_STEPS];
  }

  /**
   * Override executeFlow to add web automation specific delays between steps
   */
  async executeFlow(flow: RpaFlow, variables: Record<string, any> = {}): Promise<Record<string, any>> {
    await this.initialize(flow.settings || {}, variables);

    const context: RunnerContext = {
      variables: { ...variables },
      onLog: this.logHandler,
      cancellationChecker: this.cancellationChecker || undefined,
      flowId: flow.id
    };

    try {
      for (let i = 0; i < flow.steps.length; i++) {
        // Check for cancellation before each step
        if (context.cancellationChecker) {
          const isCancelled = await context.cancellationChecker();
          if (isCancelled) {
            context.onLog({
              level: 'info',
              message: 'Execution was cancelled, stopping flow'
            });
            throw new Error('Execution was cancelled');
          }
        }

        const step = flow.steps[i];

        // Only execute steps this runner can handle
        if (!this.canHandleStep(step.type)) {
          context.onLog({
            level: 'warn',
            message: `Step type '${step.type}' not supported by PlaywrightRunner`,
            stepId: step.id
          });
          continue;
        }

        const result = await this.executeStep(step, context);

        if (!result.success) {
          throw new Error(result.error || `Step execution failed: ${step.type}`);
        }

        // Update context variables with any results from the step
        if (result.variables) {
          context.variables = { ...context.variables, ...result.variables };
        }

        // Add random delay between web automation steps (except after the last step)
        if (i < flow.steps.length - 1) {
          const delay = this.getRandomDelay();
          context.onLog({
            level: 'info',
            message: `Waiting ${delay}ms before next step...`,
            stepId: step.id
          });
          await this.sleep(delay);
        }
      }

      return context.variables;
    } catch (error) {
      // Re-throw the error, cleanup will be handled by the caller
      throw error;
    }
  }

  async executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult> {
    if (!this.playwrightContext) {
      throw new Error('PlaywrightRunner not initialized');
    }

    const { page, variables, onLog } = this.playwrightContext;
    const timeout = step.timeout || 30000;

    onLog({
      level: 'info',
      message: `Executing step: ${step.type}`,
      stepId: step.id
    });

    try {
      switch (step.type) {
        case 'navigate':
          const interpolatedUrl = interpolateVariables(step.url, context.variables);
          await page.goto(interpolatedUrl, {
            waitUntil: step.waitUntil || 'load',
            timeout
          });
          onLog({
            level: 'info',
            message: `Navigated to ${interpolatedUrl}`,
            stepId: step.id
          });
          break;

        case 'goBack':
          await page.goBack({ timeout });
          onLog({
            level: 'info',
            message: 'Navigated back',
            stepId: step.id
          });
          break;

        case 'goForward':
          await page.goForward({ timeout });
          onLog({
            level: 'info',
            message: 'Navigated forward',
            stepId: step.id
          });
          break;

        case 'reload':
          await page.reload({ timeout });
          onLog({
            level: 'info',
            message: 'Page reloaded',
            stepId: step.id
          });
          break;

        case 'click':
          const interpolatedClickSelector = interpolateVariables(step.selector, context.variables);
          await page.click(interpolatedClickSelector, {
            button: step.button || 'left',
            clickCount: step.clickCount || 1,
            force: step.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Clicked element: ${interpolatedClickSelector}`,
            stepId: step.id
          });
          break;

        case 'fill':
          const interpolatedFillValue = interpolateVariables(step.value, context.variables);
          const interpolatedFillSelector = interpolateVariables(step.selector, context.variables);
          await page.fill(interpolatedFillSelector, interpolatedFillValue, {
            force: step.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Filled element ${interpolatedFillSelector} with: ${interpolatedFillValue}`,
            stepId: step.id
          });
          break;

        case 'type':
          const interpolatedTypeText = interpolateVariables(step.text, context.variables);
          const interpolatedTypeSelector = interpolateVariables(step.selector, context.variables);
          await page.locator(interpolatedTypeSelector).fill(interpolatedTypeText);
          onLog({
            level: 'info',
            message: `Typed in element ${interpolatedTypeSelector}: ${interpolatedTypeText}`,
            stepId: step.id
          });
          break;

        case 'selectOption':
          const interpolatedSelectSelector = interpolateVariables(step.selector, context.variables);
          const selectOptions: any = {};
          if (step.value) selectOptions.value = interpolateVariables(step.value, context.variables);
          if (step.label) selectOptions.label = interpolateVariables(step.label, context.variables);
          if (step.index !== undefined) selectOptions.index = step.index;

          await page.selectOption(interpolatedSelectSelector, selectOptions, { timeout });
          onLog({
            level: 'info',
            message: `Selected option in ${interpolatedSelectSelector}`,
            stepId: step.id
          });
          break;

        case 'check':
          const interpolatedCheckSelector = interpolateVariables(step.selector, context.variables);
          await page.check(interpolatedCheckSelector, {
            force: step.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Checked element: ${interpolatedCheckSelector}`,
            stepId: step.id
          });
          break;

        case 'uncheck':
          const interpolatedUncheckSelector = interpolateVariables(step.selector, context.variables);
          await page.uncheck(interpolatedUncheckSelector, {
            force: step.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Unchecked element: ${interpolatedUncheckSelector}`,
            stepId: step.id
          });
          break;

        case 'waitForSelector':
          const interpolatedWaitSelector = interpolateVariables(step.selector, context.variables);
          await page.waitForSelector(interpolatedWaitSelector, {
            state: step.state || 'visible',
            timeout
          });
          onLog({
            level: 'info',
            message: `Waited for selector: ${interpolatedWaitSelector}`,
            stepId: step.id
          });
          break;

        case 'waitForTimeout':
          await page.waitForTimeout(step.duration);
          onLog({
            level: 'info',
            message: `Waited for ${step.duration}ms`,
            stepId: step.id
          });
          break;

        case 'waitForUrl':
          const interpolatedWaitUrl = interpolateVariables(step.url.toString(), context.variables);
          await page.waitForURL(interpolatedWaitUrl, { timeout });
          onLog({
            level: 'info',
            message: `Waited for URL: ${interpolatedWaitUrl}`,
            stepId: step.id
          });
          break;

        case 'extractText':
          const interpolatedExtractTextSelector = interpolateVariables(step.selector, context.variables);
          const rawText = await page.textContent(interpolatedExtractTextSelector, { timeout });
          // Trim whitespace, empty lines, and spaces from beginning and end
          const text = rawText ? rawText.trim() : null;
          variables[step.variableName] = text;
          context.variables[step.variableName] = text; // Also update context variables
          onLog({
            level: 'info',
            message: `Extracted text from ${interpolatedExtractTextSelector}: ${text}`,
            stepId: step.id,
            data: { [step.variableName]: text }
          });
          break;

        case 'extractAttribute':
          const interpolatedExtractAttrSelector = interpolateVariables(step.selector, context.variables);
          const interpolatedAttribute = interpolateVariables(step.attribute, context.variables);
          const attribute = await page.getAttribute(interpolatedExtractAttrSelector, interpolatedAttribute, { timeout });
          variables[step.variableName] = attribute;
          context.variables[step.variableName] = attribute; // Also update context variables
          onLog({
            level: 'info',
            message: `Extracted attribute ${interpolatedAttribute} from ${interpolatedExtractAttrSelector}: ${attribute}`,
            stepId: step.id,
            data: { [step.variableName]: attribute }
          });
          break;

        case 'takeScreenshot':
          const takeScreenshotStep = step as any;

          // Create screenshots directory if it doesn't exist
          const screenshotsDir = path.join(process.cwd(), 'screenshots');
          if (!fs.existsSync(screenshotsDir)) {
            fs.mkdirSync(screenshotsDir, { recursive: true });
          }

          // Generate filename using flow ID instead of "screenshot"
          const flowId = context.flowId || 'unknown-flow';
          const defaultFilename = `${flowId}-${Date.now()}.png`;
          const interpolatedScreenshotPath = takeScreenshotStep.path
            ? interpolateVariables(takeScreenshotStep.path, context.variables)
            : path.join(screenshotsDir, defaultFilename);

          // Take screenshot
          await page.screenshot({
            path: interpolatedScreenshotPath,
            fullPage: takeScreenshotStep.fullPage || false
          });

          // Convert to base64 if variableName is provided
          let base64Data = null;
          if (takeScreenshotStep.variableName) {
            const imageBuffer = fs.readFileSync(interpolatedScreenshotPath);
            base64Data = imageBuffer.toString('base64');
            variables[takeScreenshotStep.variableName] = base64Data;
            context.variables[takeScreenshotStep.variableName] = base64Data;

            onLog({
              level: 'info',
              message: `Base64 data stored in variable: ${takeScreenshotStep.variableName} (${base64Data.length} characters)`,
              stepId: step.id,
              data: { variableName: takeScreenshotStep.variableName, base64Length: base64Data.length }
            });
          }

          onLog({
            level: 'info',
            message: `Screenshot saved: ${interpolatedScreenshotPath}${takeScreenshotStep.variableName ? ` and stored in variable: ${takeScreenshotStep.variableName}` : ''}`,
            stepId: step.id,
            data: {
              screenshotPath: interpolatedScreenshotPath,
              ...(takeScreenshotStep.variableName && { [takeScreenshotStep.variableName]: `base64 data (${base64Data?.length || 0} chars)` })
            }
          });
          break;

        case 'ifElementExists':
          const interpolatedIfSelector = interpolateVariables(step.selector, context.variables);
          const elementExists = await page.locator(interpolatedIfSelector).count() > 0;
          const stepsToExecute = elementExists ? step.thenSteps : (step.elseSteps || []);

          onLog({
            level: 'info',
            message: `Element ${interpolatedIfSelector} ${elementExists ? 'exists' : 'does not exist'}, executing ${elementExists ? 'then' : 'else'} steps`,
            stepId: step.id
          });

          for (const conditionalStep of stepsToExecute) {
            await this.executeStep(conditionalStep, context);
          }
          break;

        case 'fillPassword':
          const fillPasswordStep = step as any;
          const interpolatedPasswordSelector = interpolateVariables(fillPasswordStep.selector, context.variables);
          const passwordData = await credentialService.getDecryptedPassword(fillPasswordStep.credentialId);
          if (!passwordData) {
            throw new Error(`Password credential with id ${fillPasswordStep.credentialId} not found`);
          }

          await page.fill(interpolatedPasswordSelector, passwordData.password, {
            force: fillPasswordStep.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Filled password field ${interpolatedPasswordSelector} with credential: ${passwordData.username}`,
            stepId: fillPasswordStep.id
          });
          break;

        case 'fill2FA':
          const fill2FAStep = step as any;
          const interpolated2FASelector = interpolateVariables(fill2FAStep.selector, context.variables);
          const totpCode = await credentialService.generateTOTPCode(fill2FAStep.credentialId);
          if (!totpCode) {
            throw new Error(`2FA credential with id ${fill2FAStep.credentialId} not found`);
          }

          await page.fill(interpolated2FASelector, totpCode, {
            force: fill2FAStep.force,
            timeout
          });
          onLog({
            level: 'info',
            message: `Filled 2FA field ${interpolated2FASelector} with generated TOTP code`,
            stepId: fill2FAStep.id
          });
          break;

        case 'conditionalClick':
          const conditionalClickStep = step as any;
          const interpolatedConditionalSelector = interpolateVariables(conditionalClickStep.selector, context.variables);
          let shouldClick = false;
          let conditionResult = false;

          try {
            // Check the condition
            switch (conditionalClickStep.condition) {
              case 'exists':
                conditionResult = await page.locator(interpolatedConditionalSelector).count() > 0;
                break;
              case 'enabled':
                const enabledElement = page.locator(interpolatedConditionalSelector);
                conditionResult = await enabledElement.count() > 0 && await enabledElement.isEnabled();
                break;
              case 'disabled':
                const disabledElement = page.locator(interpolatedConditionalSelector);
                conditionResult = await disabledElement.count() > 0 && await disabledElement.isDisabled();
                break;
              default:
                throw new Error(`Unknown condition: ${conditionalClickStep.condition}`);
            }

            // Determine if we should click based on condition result and clickIfTrue setting
            shouldClick = conditionalClickStep.clickIfTrue ? conditionResult : !conditionResult;

            onLog({
              level: 'info',
              message: `Condition '${conditionalClickStep.condition}' for ${interpolatedConditionalSelector}: ${conditionResult}. ${shouldClick ? 'Will click' : 'Will not click'}.`,
              stepId: conditionalClickStep.id
            });

            if (shouldClick) {
              await page.click(interpolatedConditionalSelector, {
                button: conditionalClickStep.button || 'left',
                force: conditionalClickStep.force,
                timeout
              });
              onLog({
                level: 'info',
                message: `Clicked ${interpolatedConditionalSelector} (${conditionalClickStep.button || 'left'} button)`,
                stepId: conditionalClickStep.id
              });
            } else {
              onLog({
                level: 'info',
                message: `Skipped clicking ${interpolatedConditionalSelector} due to condition`,
                stepId: conditionalClickStep.id
              });
            }
          } catch (conditionError) {
            const conditionErrorMessage = conditionError instanceof Error ? conditionError.message : 'Unknown error';
            onLog({
              level: 'warn',
              message: `Could not evaluate condition for ${interpolatedConditionalSelector}: ${conditionErrorMessage}. Skipping click.`,
              stepId: conditionalClickStep.id
            });
          }
          break;

        default:
          return {
            success: false,
            error: `Unknown step type: ${(step as any).type}`
          };
      }

      return {
        success: true,
        variables: context.variables
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onLog({
        level: 'error',
        message: `Error executing step ${step.type}: ${errorMessage}`,
        stepId: step.id,
        data: { error: errorMessage }
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async cleanup(): Promise<void> {
    if (this.playwrightContext) {
      try {
        // Close browser context first
        if (this.playwrightContext.context) {
          await this.playwrightContext.context.close();
        }
      } catch (error) {
        console.error('Error closing browser context:', error);
      }

      try {
        // Close browser instance
        if (this.playwrightContext.browser) {
          await this.playwrightContext.browser.close();
        }
      } catch (error) {
        console.error('Error closing browser:', error);
      }

      this.playwrightContext = undefined;
      console.log('🧹 PlaywrightRunner cleanup completed');
    }
  }

  setLogHandler(onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void): void {
    this.logHandler = onLog;
    if (this.playwrightContext) {
      this.playwrightContext.onLog = onLog;
    }
  }

  /**
   * Generate random delay between web automation steps (1-5 seconds)
   * Change the values below to adjust timing
   */
  private getRandomDelay(): number {
    const minDelay = 1000;  // 1 second - change this value
    const maxDelay = 5000; // 5 seconds - change this value
    return Math.floor(Math.random() * (maxDelay - minDelay)) + minDelay;
  }

  /**
   * Sleep utility function for waitForTimeout steps and delays between steps
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
