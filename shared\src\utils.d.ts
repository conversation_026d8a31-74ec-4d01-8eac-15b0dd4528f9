export declare function formatDuration(ms: number): string;
/**
 * Variable interpolation utilities for RPA flows
 */
/**
 * Interpolates variables in a string using ${variableName} syntax
 * @param text The text containing variable references
 * @param variables The variables object containing values
 * @returns The text with variables replaced by their values
 */
export declare function interpolateVariables(text: string, variables: Record<string, any>): string;
/**
 * Checks if a string contains variable references
 * @param text The text to check
 * @returns True if the text contains ${variableName} patterns
 */
export declare function hasVariableReferences(text: string): boolean;
/**
 * Extracts all variable names referenced in a string
 * @param text The text to analyze
 * @returns Array of variable names found in the text
 */
export declare function extractVariableNames(text: string): string[];
/**
 * Validates that all variable references in a text can be resolved
 * @param text The text containing variable references
 * @param variables The available variables
 * @returns Object with validation result and missing variables
 */
export declare function validateVariableReferences(text: string, variables: Record<string, any>): {
    valid: boolean;
    missingVariables: string[];
};
//# sourceMappingURL=utils.d.ts.map