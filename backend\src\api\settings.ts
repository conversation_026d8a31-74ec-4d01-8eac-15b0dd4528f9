import { Router, Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import { ApiResponse } from '@rpa-project/shared';
import { setMasterKey } from '../utils/encryption';

const router = Router();

// Validation schema for master key
const masterKeySchema = Joi.object({
  masterKey: Joi.string().required().length(64).pattern(/^[0-9a-fA-F]+$/)
});

// POST /api/settings/master-key - Update master key
router.post('/master-key', async (req: Request, res: Response) => {
  try {
    const { error, value } = masterKeySchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Master key must be exactly 64 hexadecimal characters'
      };
      return res.status(400).json(response);
    }

    const { masterKey } = value;
    
    // Set the new master key
    setMasterKey(masterKey);

    const response: ApiResponse = {
      success: true,
      data: { message: 'Master key updated successfully' }
    };

    res.json(response);
  } catch (error) {
    console.error('Error updating master key:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

export { router as settingsRoutes };
