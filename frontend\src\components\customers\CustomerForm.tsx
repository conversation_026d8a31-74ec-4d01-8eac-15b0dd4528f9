import { useState, useEffect } from 'react'
import { Customer, CreateCustomerRequest, UpdateCustomerRequest } from '@rpa-project/shared'
import { customerApi } from '../../services/api'
import { Portal } from '../ui/Portal'

interface CustomerFormProps {
  customer?: Customer | null
  onSuccess: (customerId?: string) => void
  onCancel: () => void
}

export function CustomerForm({ customer, onSuccess, onCancel }: CustomerFormProps) {
  const [formData, setFormData] = useState({
    customerNumber: '',
    name: '',
    vismaNumber: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const isEditing = !!customer

  useEffect(() => {
    if (customer) {
      setFormData({
        customerNumber: customer.customerNumber,
        name: customer.name,
        vismaNumber: customer.vismaNumber || ''
      })
    }
  }, [customer])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      if (isEditing) {
        const updateRequest: UpdateCustomerRequest = {
          customerNumber: formData.customerNumber || undefined,
          name: formData.name || undefined,
          vismaNumber: formData.vismaNumber || undefined
        }

        // Remove empty fields
        Object.keys(updateRequest).forEach(key => {
          if (updateRequest[key as keyof UpdateCustomerRequest] === '') {
            delete updateRequest[key as keyof UpdateCustomerRequest]
          }
        })

        const response = await customerApi.updateCustomer(customer!.id, updateRequest)
        if (!response.success) {
          throw new Error(response.error || 'Failed to update customer')
        }
        onSuccess()
      } else {
        const createRequest: CreateCustomerRequest = {
          customerNumber: formData.customerNumber,
          name: formData.name,
          vismaNumber: formData.vismaNumber || undefined
        }

        const response = await customerApi.createCustomer(createRequest)
        if (!response.success) {
          throw new Error(response.error || 'Failed to create customer')
        }

        // Pass the created customer ID to onSuccess for navigation
        onSuccess(response.data?.id)
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <Portal>
      <div className="modal-overlay" onClick={onCancel}>
        <div
          className="modal-content"
          style={{ width: '100%', maxWidth: '70vw' }}
          onClick={(e) => e.stopPropagation()}
        >
        {/* Modal Header */}
        <div style={{
          flexShrink: 0,
          borderBottom: '1px solid #e5e7eb',
          margin: 0,
          padding: '1.5rem',
          textAlign: 'left'
        }}>
          <p className="dashboard-title" style={{ fontSize: '1.5rem', margin: 0 }}>
            {isEditing ? 'Redigera kund' : 'Ny kund'}
          </p>
        </div>

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <div style={{
            flex: 1,
            overflow: 'auto',
            padding: '1.5rem'
          }}>
            {error && (
              <div className="error-card" style={{ marginBottom: '1rem' }}>
                <h3 className="error-title">Fel</h3>
                <p className="error-message">{error}</p>
              </div>
            )}

            <div className="form-group">
              <label className="form-label">
                Kundnummer *
              </label>
              <input
                type="text"
                className="form-input"
                value={formData.customerNumber}
                onChange={(e) => handleChange('customerNumber', e.target.value)}
                placeholder="Ange kundnummer"
                required
                maxLength={50}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                Unik identifierare för kunden
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">
                Kundnamn *
              </label>
              <input
                type="text"
                className="form-input"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder="Ange kundnamn"
                required
                maxLength={200}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                Fullständigt namn på kunden (max 200 tecken)
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">
                Visma-nummer
              </label>
              <input
                type="text"
                className="form-input"
                value={formData.vismaNumber}
                onChange={(e) => handleChange('vismaNumber', e.target.value)}
                placeholder="Ange Visma-nummer (valfritt)"
                maxLength={50}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                Valfri Visma-systemidentifierare
              </div>
            </div>

            {/* Note about token management */}
            <div style={{
              backgroundColor: '#f8fafc',
              border: '1px solid #e2e8f0',
              borderRadius: '0.5rem',
              padding: '1rem',
              marginTop: '1rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                <span>🔑</span>
                <span style={{ fontWeight: '500', color: '#374151' }}>API Token Hantering</span>
              </div>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                API tokens och refresh tokens hanteras nu separat efter att kunden har skapats.
                Du kan lägga till och hantera flera token-par för varje kund via kundens detaljsida.
              </p>
            </div>
          </div>

          <div style={{
            flexShrink: 0,
            borderTop: '1px solid #e5e7eb',
            padding: '1.5rem',
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '0.75rem'
          }}>
            <button
              type="button"
              onClick={onCancel}
              disabled={loading}
              className="action-button secondary"
              style={{
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.5 : 1
              }}
            >
              <span>Avbryt</span>
            </button>
            <button
              type="submit"
              disabled={loading || !formData.customerNumber.trim() || !formData.name.trim()}
              className="action-button primary"
              style={{
                cursor: (loading || !formData.customerNumber.trim() || !formData.name.trim()) ? 'not-allowed' : 'pointer',
                opacity: (loading || !formData.customerNumber.trim() || !formData.name.trim()) ? 0.5 : 1
              }}
            >
              <span>{loading ? 'Sparar...' : (isEditing ? 'Uppdatera kund' : 'Skapa kund')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
    </Portal>
  )
}
