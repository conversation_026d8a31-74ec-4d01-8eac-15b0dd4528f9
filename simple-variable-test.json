{"name": "Enkelt Variabel Test", "description": "<PERSON>tt enkelt test för att visa hur variabler fungerar", "customerId": "test-customer-001", "steps": [{"id": "step1", "type": "navigate", "url": "https://example.com", "waitUntil": "load", "timeout": 30000, "description": "Gå till example.com"}, {"id": "step2", "type": "extractText", "selector": "h1", "variableName": "siteTitle", "timeout": 10000, "description": "<PERSON><PERSON><PERSON> sidan<PERSON> huvu<PERSON>"}, {"id": "step3", "type": "extractText", "selector": "p", "variableName": "siteDescription", "timeout": 10000, "description": "<PERSON><PERSON><PERSON> första stycket"}, {"id": "step4", "type": "navigate", "url": "https://httpbin.org/forms/post", "waitUntil": "load", "timeout": 30000, "description": "Gå till testformulär"}, {"id": "step5", "type": "fill", "selector": "input[name='custname']", "value": "Titel: ${siteTitle}", "timeout": 10000, "description": "Fyll i kundnamn med extraherad titel"}, {"id": "step6", "type": "fill", "selector": "textarea[name='comments']", "value": "Beskrivning: ${siteDescription}. Titel var: ${siteTitle}", "timeout": 10000, "description": "F<PERSON>l i kommentarer med båda variablerna"}, {"id": "step7", "type": "takeScreenshot", "path": "test-${siteTitle}.png", "fullPage": false, "timeout": 5000, "description": "Ta skärmbild med dynamiskt filnamn"}], "settings": {"browser": "chromium", "headless": false, "viewport": {"width": 1280, "height": 720}}}