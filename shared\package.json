{"name": "@rpa-project/shared", "version": "1.0.0", "description": "Shared TypeScript types and utilities for RPA project", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "scripts": {"build": "npm run build:esm && npm run build:cjs", "build:esm": "tsc -p tsconfig.esm.json", "build:cjs": "tsc -p tsconfig.cjs.json", "dev": "tsc --watch", "clean": "rm -rf dist"}, "devDependencies": {"typescript": "^5.3.3"}, "files": ["dist"]}