import { Router, Request, Response } from 'express';
import { ApiResponse } from '@rpa-project/shared';
import { asyncHandler } from '../middleware/errorHandler';
import { QueueService } from '../queue/queueService';

const router = Router();
const queueService = new QueueService();

// GET /api/queue/stats - Get queue statistics
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  const stats = await queueService.getQueueStats();
  
  const response: ApiResponse = {
    success: true,
    data: stats
  };
  
  res.json(response);
}));

// GET /api/queue/jobs - Get queue jobs
router.get('/jobs', asyncHandler(async (req: Request, res: Response) => {
  const { status, limit = 50, offset = 0 } = req.query;
  
  const jobs = await queueService.getJobs({
    status: status as any,
    limit: parseInt(limit as string),
    offset: parseInt(offset as string)
  });
  
  const response: ApiResponse = {
    success: true,
    data: jobs
  };
  
  res.json(response);
}));

// POST /api/queue/pause - Pause queue
router.post('/pause', asyncHandler(async (req: Request, res: Response) => {
  await queueService.pauseQueue();
  
  const response: ApiResponse = {
    success: true,
    message: 'Queue paused'
  };
  
  res.json(response);
}));

// POST /api/queue/resume - Resume queue
router.post('/resume', asyncHandler(async (req: Request, res: Response) => {
  await queueService.resumeQueue();
  
  const response: ApiResponse = {
    success: true,
    message: 'Queue resumed'
  };
  
  res.json(response);
}));

// DELETE /api/queue/clean - Clean completed jobs
router.delete('/clean', asyncHandler(async (req: Request, res: Response) => {
  const { olderThan = 24 } = req.query; // hours
  
  const cleaned = await queueService.cleanCompletedJobs(parseInt(olderThan as string));
  
  const response: ApiResponse = {
    success: true,
    data: { cleaned },
    message: `Cleaned ${cleaned} completed jobs`
  };
  
  res.json(response);
}));

export { router as queueRoutes };
