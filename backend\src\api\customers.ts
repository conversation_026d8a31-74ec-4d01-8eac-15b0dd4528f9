import { Router, Request, Response } from 'express';
import Jo<PERSON> from 'joi';
import {
  CreateCustomerRequest,
  UpdateCustomerRequest,
  CreateCustomerTokenRequest,
  UpdateCustomerTokenRequest,
  ApiResponse
} from '@rpa-project/shared';
import { customerService } from '../services/customerService';

const router = Router();

// Validation schemas
const createCustomerSchema = Joi.object({
  customerNumber: Joi.string().required().min(1).max(50),
  name: Joi.string().required().min(1).max(200),
  vismaNumber: Joi.string().optional().max(50),
  apiToken: Joi.string().optional().min(1),
  refreshToken: Joi.string().optional().min(1)
});

const updateCustomerSchema = Joi.object({
  customerNumber: Joi.string().optional().min(1).max(50),
  name: Joi.string().optional().min(1).max(200),
  vismaNumber: Joi.string().optional().max(50),
  apiToken: Joi.string().optional().min(1),
  refreshToken: Joi.string().optional().min(1)
});

const createCustomerTokenSchema = Joi.object({
  name: Joi.string().required().min(1).max(100),
  description: Joi.string().optional().max(500),
  apiToken: Joi.string().optional().min(1),
  refreshToken: Joi.string().optional().min(1)
});

const updateCustomerTokenSchema = Joi.object({
  name: Joi.string().optional().min(1).max(100),
  description: Joi.string().optional().max(500),
  apiToken: Joi.string().optional().min(1),
  refreshToken: Joi.string().optional().min(1)
});

// GET /api/customers - Get all customers
router.get('/', async (req: Request, res: Response) => {
  try {
    const customers = await customerService.getAllCustomers();
    const response: ApiResponse = {
      success: true,
      data: customers
    };
    res.json(response);
  } catch (error) {
    console.error('Error getting customers:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get customers'
    };
    res.status(500).json(response);
  }
});

// GET /api/customers/:id - Get customer by ID
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const customer = await customerService.getCustomer(id);
    
    if (!customer) {
      const response: ApiResponse = {
        success: false,
        error: 'Customer not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: customer
    };
    res.json(response);
  } catch (error) {
    console.error('Error getting customer:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get customer'
    };
    res.status(500).json(response);
  }
});

// GET /api/customers/number/:customerNumber - Get customer by customer number
router.get('/number/:customerNumber', async (req: Request, res: Response) => {
  try {
    const { customerNumber } = req.params;
    const customer = await customerService.getCustomerByNumber(customerNumber);
    
    if (!customer) {
      const response: ApiResponse = {
        success: false,
        error: 'Customer not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: customer
    };
    res.json(response);
  } catch (error) {
    console.error('Error getting customer by number:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get customer'
    };
    res.status(500).json(response);
  }
});

// POST /api/customers - Create new customer
router.post('/', async (req: Request, res: Response) => {
  try {
    const { error, value } = createCustomerSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }

    const request: CreateCustomerRequest = value;
    const customer = await customerService.createCustomer(request);

    const response: ApiResponse = {
      success: true,
      data: customer
    };
    res.status(201).json(response);
  } catch (error: any) {
    console.error('Error creating customer:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to create customer'
    };
    res.status(400).json(response);
  }
});

// PUT /api/customers/:id - Update customer
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { error, value } = updateCustomerSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }

    const request: UpdateCustomerRequest = value;
    const customer = await customerService.updateCustomer(id, request);

    const response: ApiResponse = {
      success: true,
      data: customer
    };
    res.json(response);
  } catch (error: any) {
    console.error('Error updating customer:', error);
    const statusCode = error.message === 'Customer not found' ? 404 : 400;
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to update customer'
    };
    res.status(statusCode).json(response);
  }
});

// DELETE /api/customers/:id - Delete customer
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const deleted = await customerService.deleteCustomer(id);

    if (!deleted) {
      const response: ApiResponse = {
        success: false,
        error: 'Customer not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: { deleted: true }
    };
    res.json(response);
  } catch (error: any) {
    console.error('Error deleting customer:', error);
    const statusCode = error.message === 'Customer not found' ? 404 : 500;
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to delete customer'
    };
    res.status(statusCode).json(response);
  }
});





// Customer Token Management Endpoints

// GET /api/customers/:id/tokens - Get all tokens for a customer
router.get('/:id/tokens', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tokens = await customerService.getCustomerTokensByCustomerId(id);

    const response: ApiResponse = {
      success: true,
      data: tokens
    };
    res.json(response);
  } catch (error) {
    console.error('Error getting customer tokens:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get customer tokens'
    };
    res.status(500).json(response);
  }
});

// POST /api/customers/:id/tokens - Create a new token for a customer
router.post('/:id/tokens', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Validate request body
    const { error, value } = createCustomerTokenSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }

    const createRequest: CreateCustomerTokenRequest = value;
    const token = await customerService.createCustomerToken(id, createRequest);

    const response: ApiResponse = {
      success: true,
      data: token
    };
    res.status(201).json(response);
  } catch (error: any) {
    console.error('Error creating customer token:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to create customer token'
    };
    res.status(500).json(response);
  }
});

// GET /api/customers/:customerId/tokens/:tokenId - Get a specific token
router.get('/:customerId/tokens/:tokenId', async (req: Request, res: Response) => {
  try {
    const { tokenId } = req.params;
    const token = await customerService.getCustomerToken(tokenId);

    if (!token) {
      const response: ApiResponse = {
        success: false,
        error: 'Customer token not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: token
    };
    res.json(response);
  } catch (error) {
    console.error('Error getting customer token:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get customer token'
    };
    res.status(500).json(response);
  }
});

// PUT /api/customers/:customerId/tokens/:tokenId - Update a specific token
router.put('/:customerId/tokens/:tokenId', async (req: Request, res: Response) => {
  try {
    const { tokenId } = req.params;

    // Validate request body
    const { error, value } = updateCustomerTokenSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }

    const updateRequest: UpdateCustomerTokenRequest = value;
    const token = await customerService.updateCustomerToken(tokenId, updateRequest);

    if (!token) {
      const response: ApiResponse = {
        success: false,
        error: 'Customer token not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: token
    };
    res.json(response);
  } catch (error: any) {
    console.error('Error updating customer token:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to update customer token'
    };
    res.status(500).json(response);
  }
});

// DELETE /api/customers/:customerId/tokens/:tokenId - Delete a specific token
router.delete('/:customerId/tokens/:tokenId', async (req: Request, res: Response) => {
  try {
    const { tokenId } = req.params;
    const success = await customerService.deleteCustomerToken(tokenId);

    if (!success) {
      const response: ApiResponse = {
        success: false,
        error: 'Customer token not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      message: 'Customer token deleted successfully'
    };
    res.json(response);
  } catch (error: any) {
    console.error('Error deleting customer token:', error);
    const response: ApiResponse = {
      success: false,
      error: error.message || 'Failed to delete customer token'
    };
    res.status(500).json(response);
  }
});

// GET /api/customers/:customerId/tokens/:tokenId/data - Get decrypted token data (for internal use)
router.get('/:customerId/tokens/:tokenId/data', async (req: Request, res: Response) => {
  try {
    const { tokenId } = req.params;
    const tokenData = await customerService.getCustomerTokenData(tokenId);

    if (!tokenData) {
      const response: ApiResponse = {
        success: false,
        error: 'Customer token not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: tokenData
    };
    res.json(response);
  } catch (error) {
    console.error('Error getting customer token data:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get customer token data'
    };
    res.status(500).json(response);
  }
});

export default router;
