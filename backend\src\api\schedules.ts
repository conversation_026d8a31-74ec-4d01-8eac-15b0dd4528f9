import { Router, Request, Response } from 'express';
import { 
  FlowSchedule, 
  CreateScheduleRequest, 
  UpdateScheduleRequest, 
  ScheduleQuery,
  ApiResponse,
  CronExpressionInfo
} from '@rpa-project/shared';
import { ScheduleService } from '../services/scheduleService';
import { getScheduler } from '../queue/scheduler';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();
const scheduleService = new ScheduleService();

// GET /api/schedules - Get all schedules
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const query: ScheduleQuery = {
    flowId: req.query.flowId as string,
    enabled: req.query.enabled ? req.query.enabled === 'true' : undefined,
    limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
    offset: req.query.offset ? parseInt(req.query.offset as string) : undefined
  };

  const schedules = await scheduleService.getSchedules(query);
  
  const response: ApiResponse<FlowSchedule[]> = {
    success: true,
    data: schedules,
    message: `Found ${schedules.length} schedules`
  };
  
  res.json(response);
}));

// GET /api/schedules/:id - Get schedule by ID
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const schedule = await scheduleService.getScheduleById(id);
  
  if (!schedule) {
    const response: ApiResponse = {
      success: false,
      error: 'Schedule not found'
    };
    return res.status(404).json(response);
  }
  
  const response: ApiResponse<FlowSchedule> = {
    success: true,
    data: schedule
  };
  
  res.json(response);
}));

// POST /api/schedules - Create new schedule
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const createRequest: CreateScheduleRequest = req.body;
  
  // Validate required fields
  if (!createRequest.flowId || !createRequest.name || !createRequest.cronExpression) {
    const response: ApiResponse = {
      success: false,
      error: 'Missing required fields: flowId, name, cronExpression'
    };
    return res.status(400).json(response);
  }
  
  try {
    const schedule = await scheduleService.createSchedule(createRequest);
    
    // Add to scheduler if enabled
    if (schedule.enabled) {
      const scheduler = getScheduler();
      await scheduler.addSchedule(schedule);
    }
    
    const response: ApiResponse<FlowSchedule> = {
      success: true,
      data: schedule,
      message: 'Schedule created successfully'
    };
    
    res.status(201).json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create schedule'
    };
    res.status(400).json(response);
  }
}));

// PUT /api/schedules/:id - Update schedule
router.put('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateRequest: UpdateScheduleRequest = req.body;
  
  try {
    const schedule = await scheduleService.updateSchedule(id, updateRequest);
    
    if (!schedule) {
      const response: ApiResponse = {
        success: false,
        error: 'Schedule not found'
      };
      return res.status(404).json(response);
    }
    
    // Update scheduler
    const scheduler = getScheduler();
    await scheduler.updateSchedule(schedule);
    
    const response: ApiResponse<FlowSchedule> = {
      success: true,
      data: schedule,
      message: 'Schedule updated successfully'
    };
    
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update schedule'
    };
    res.status(400).json(response);
  }
}));

// DELETE /api/schedules/:id - Delete schedule
router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const deleted = await scheduleService.deleteSchedule(id);
  
  if (!deleted) {
    const response: ApiResponse = {
      success: false,
      error: 'Schedule not found'
    };
    return res.status(404).json(response);
  }
  
  // Remove from scheduler
  const scheduler = getScheduler();
  await scheduler.removeSchedule(id);
  
  const response: ApiResponse = {
    success: true,
    message: 'Schedule deleted successfully'
  };
  
  res.json(response);
}));

// POST /api/schedules/:id/toggle - Toggle schedule enabled/disabled
router.post('/:id/toggle', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const existingSchedule = await scheduleService.getScheduleById(id);
  if (!existingSchedule) {
    const response: ApiResponse = {
      success: false,
      error: 'Schedule not found'
    };
    return res.status(404).json(response);
  }
  
  const schedule = await scheduleService.updateSchedule(id, {
    enabled: !existingSchedule.enabled
  });
  
  if (!schedule) {
    const response: ApiResponse = {
      success: false,
      error: 'Failed to toggle schedule'
    };
    return res.status(500).json(response);
  }
  
  // Update scheduler
  const scheduler = getScheduler();
  await scheduler.updateSchedule(schedule);
  
  const response: ApiResponse<FlowSchedule> = {
    success: true,
    data: schedule,
    message: `Schedule ${schedule.enabled ? 'enabled' : 'disabled'}`
  };
  
  res.json(response);
}));

// POST /api/schedules/validate-cron - Validate cron expression
router.post('/validate-cron', asyncHandler(async (req: Request, res: Response) => {
  const { cronExpression, timezone } = req.body;
  
  if (!cronExpression) {
    const response: ApiResponse = {
      success: false,
      error: 'cronExpression is required'
    };
    return res.status(400).json(response);
  }
  
  const cronInfo = scheduleService.validateCronExpression(cronExpression, timezone);
  
  const response: ApiResponse<CronExpressionInfo> = {
    success: true,
    data: cronInfo
  };
  
  res.json(response);
}));

// GET /api/schedules/flow/:flowId - Get schedules for a specific flow
router.get('/flow/:flowId', asyncHandler(async (req: Request, res: Response) => {
  const { flowId } = req.params;
  
  const schedules = await scheduleService.getSchedulesByFlowId(flowId);
  
  const response: ApiResponse<FlowSchedule[]> = {
    success: true,
    data: schedules,
    message: `Found ${schedules.length} schedules for flow ${flowId}`
  };
  
  res.json(response);
}));

// GET /api/schedules/stats - Get scheduler statistics
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  const scheduler = getScheduler();
  const stats = await scheduler.getSchedulerStats();
  
  const response: ApiResponse = {
    success: true,
    data: stats
  };
  
  res.json(response);
}));

export { router as scheduleRoutes };
