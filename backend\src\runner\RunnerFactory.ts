import { <PERSON><PERSON><PERSON><PERSON> } from './IRunner';
import { <PERSON><PERSON>Runner } from './playwrightRunner';
import { RunnerRegistry, getRunnerRegistry } from './RunnerRegistry';
import { RunnerType, getRunnerTypeForStep, isRunnerImplemented } from './stepTypes';
import { RpaFlow, RpaStep, ExecutionLog } from '@rpa-project/shared';

/**
 * Configuration for the runner factory
 */
export interface RunnerFactoryConfig {
  logHandler?: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  cancellationChecker?: () => Promise<boolean>;
}

/**
 * Factory for creating and managing RPA runners
 */
export class RunnerFactory {
  private static instance: RunnerFactory;
  private registry: RunnerRegistry;

  private constructor() {
    this.registry = getRunnerRegistry();
    this.initializeDefaultRunners();
  }

  /**
   * Get singleton instance of the factory
   */
  public static getInstance(): RunnerFactory {
    if (!RunnerFactory.instance) {
      RunnerFactory.instance = new RunnerFactory();
    }
    return RunnerFactory.instance;
  }

  /**
   * Initialize default runners
   */
  private initializeDefaultRunners(): void {
    // Register PlaywrightRunner for web automation
    this.registry.registerRunner(RunnerType.PLAYWRIGHT, PlaywrightRunner);
    
    // Future runners can be registered here:
    // this.registry.registerRunner(RunnerType.API, ApiRunner);
    // this.registry.registerRunner(RunnerType.DATABASE, DatabaseRunner);
    // this.registry.registerRunner(RunnerType.FILE_SYSTEM, FileSystemRunner);
  }

  /**
   * Create a runner for a specific step type
   */
  public createRunnerForStep(stepType: string, config?: RunnerFactoryConfig): IRunner {
    const runnerType = getRunnerTypeForStep(stepType);
    
    if (!isRunnerImplemented(runnerType)) {
      throw new Error(`Runner type '${runnerType}' is not yet implemented for step type '${stepType}'`);
    }

    const runner = this.registry.createRunner(runnerType);
    
    // Configure the runner
    if (config?.logHandler) {
      runner.setLogHandler(config.logHandler);
    }
    
    if (config?.cancellationChecker) {
      runner.setCancellationChecker(config.cancellationChecker);
    }

    return runner;
  }

  /**
   * Create a runner for a specific runner type
   */
  public createRunner(runnerType: RunnerType, config?: RunnerFactoryConfig): IRunner {
    if (!isRunnerImplemented(runnerType)) {
      throw new Error(`Runner type '${runnerType}' is not yet implemented`);
    }

    const runner = this.registry.createRunner(runnerType);
    
    // Configure the runner
    if (config?.logHandler) {
      runner.setLogHandler(config.logHandler);
    }
    
    if (config?.cancellationChecker) {
      runner.setCancellationChecker(config.cancellationChecker);
    }

    return runner;
  }

  /**
   * Get or create a cached runner for a specific execution and step type
   */
  public getRunnerForExecution(executionId: string, stepType: string, config?: RunnerFactoryConfig): IRunner {
    const runner = this.registry.getRunnerForStep(executionId, stepType);

    // Configure the runner if config is provided
    if (config?.logHandler) {
      runner.setLogHandler(config.logHandler);
    }

    if (config?.cancellationChecker) {
      runner.setCancellationChecker(config.cancellationChecker);
    }

    return runner;
  }

  /**
   * Get or create a cached runner for a specific execution and runner type
   */
  public getOrCreateRunner(executionId: string, runnerType: RunnerType, config?: RunnerFactoryConfig): IRunner {
    const runner = this.registry.getOrCreateRunner(executionId, runnerType);

    // Configure the runner if config is provided
    if (config?.logHandler) {
      runner.setLogHandler(config.logHandler);
    }

    if (config?.cancellationChecker) {
      runner.setCancellationChecker(config.cancellationChecker);
    }

    return runner;
  }

  /**
   * Check if a step type is supported
   */
  public isStepTypeSupported(stepType: string): boolean {
    return this.registry.isStepTypeSupported(stepType);
  }

  /**
   * Get all supported step types
   */
  public getSupportedStepTypes(): string[] {
    const supportedTypes: string[] = [];
    
    for (const runnerType of this.registry.getRegisteredRunnerTypes()) {
      if (isRunnerImplemented(runnerType)) {
        const runner = this.registry.createRunner(runnerType);
        supportedTypes.push(...runner.getSupportedStepTypes());
        // Clean up the temporary runner
        runner.cleanup().catch(console.error);
      }
    }
    
    return supportedTypes;
  }

  /**
   * Analyze a flow and determine which runners are needed
   */
  public analyzeFlow(flow: RpaFlow): {
    requiredRunners: RunnerType[];
    unsupportedSteps: string[];
    stepsByRunner: Record<RunnerType, string[]>;
  } {
    const requiredRunners = new Set<RunnerType>();
    const unsupportedSteps: string[] = [];
    const stepsByRunner: Record<RunnerType, string[]> = {} as any;

    for (const step of flow.steps) {
      try {
        const runnerType = getRunnerTypeForStep(step.type);
        
        if (isRunnerImplemented(runnerType)) {
          requiredRunners.add(runnerType);
          
          if (!stepsByRunner[runnerType]) {
            stepsByRunner[runnerType] = [];
          }
          stepsByRunner[runnerType].push(step.type);
        } else {
          unsupportedSteps.push(step.type);
        }
      } catch {
        unsupportedSteps.push(step.type);
      }
    }

    return {
      requiredRunners: Array.from(requiredRunners),
      unsupportedSteps,
      stepsByRunner
    };
  }

  /**
   * Clean up all runners for a specific execution
   */
  public async cleanupExecution(executionId: string): Promise<void> {
    await this.registry.cleanupExecution(executionId);
  }

  /**
   * Get factory statistics
   */
  public getStats(): {
    registeredRunners: number;
    activeInstances: number;
    supportedStepTypes: number;
  } {
    const registryStats = this.registry.getStats();
    
    return {
      registeredRunners: registryStats.registeredRunners,
      activeInstances: registryStats.activeInstances,
      supportedStepTypes: this.getSupportedStepTypes().length
    };
  }

  /**
   * Reset the factory (mainly for testing)
   */
  public reset(): void {
    this.registry.reset();
    this.initializeDefaultRunners();
  }
}

/**
 * Get the global runner factory instance
 */
export function getRunnerFactory(): RunnerFactory {
  return RunnerFactory.getInstance();
}
