{"name": "rpa-project", "version": "1.0.0", "description": "Complete RPA project with TypeScript, React Flow, and Playwright", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:shared": "cd shared && npm run build", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../shared && npm install", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm run install:all && npm run build", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules shared/node_modules"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"cron-parser": "^5.3.0"}}