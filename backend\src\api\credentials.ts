import { Router, Request, Response } from 'express';
import Jo<PERSON> from 'joi';
import { 
  CreatePasswordCredentialRequest,
  CreateTwoFactorCredentialRequest,
  UpdatePasswordCredentialRequest,
  UpdateTwoFactorCredentialRequest,
  ApiResponse 
} from '@rpa-project/shared';
import { credentialService } from '../services/credentialService';

const router = Router();

// Validation schemas
const createPasswordCredentialSchema = Joi.object({
  name: Joi.string().required().min(1).max(100),
  description: Joi.string().optional().max(500),
  username: Joi.string().required().min(1).max(100),
  password: Joi.string().required().min(1)
});

const createTwoFactorCredentialSchema = Joi.object({
  name: Joi.string().required().min(1).max(100),
  description: Joi.string().optional().max(500),
  secret: Joi.string().required().min(1)
});

const updatePasswordCredentialSchema = Joi.object({
  name: Joi.string().optional().min(1).max(100),
  description: Joi.string().optional().max(500),
  username: Joi.string().optional().min(1).max(100),
  password: Joi.string().optional().min(1)
});

const updateTwoFactorCredentialSchema = Joi.object({
  name: Joi.string().optional().min(1).max(100),
  description: Joi.string().optional().max(500),
  secret: Joi.string().optional().min(1)
});

// GET /api/credentials - Get all credentials
router.get('/', async (req: Request, res: Response) => {
  try {
    const type = req.query.type as string;
    
    let credentials;
    if (type && (type === 'password' || type === '2fa')) {
      credentials = await credentialService.getCredentialsByType(type);
    } else {
      credentials = await credentialService.getAllCredentials();
    }

    const response: ApiResponse = {
      success: true,
      data: credentials
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting credentials:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// GET /api/credentials/:id - Get specific credential
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const credential = await credentialService.getCredential(id);

    if (!credential) {
      const response: ApiResponse = {
        success: false,
        error: 'Credential not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: credential
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting credential:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// POST /api/credentials/password - Create password credential
router.post('/password', async (req: Request, res: Response) => {
  try {
    const { error, value } = createPasswordCredentialSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }

    const request: CreatePasswordCredentialRequest = value;
    const credential = await credentialService.createPasswordCredential(request);

    const response: ApiResponse = {
      success: true,
      data: credential
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Error creating password credential:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// POST /api/credentials/2fa - Create 2FA credential
router.post('/2fa', async (req: Request, res: Response) => {
  try {
    const { error, value } = createTwoFactorCredentialSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }

    const request: CreateTwoFactorCredentialRequest = value;
    const credential = await credentialService.createTwoFactorCredential(request);

    const response: ApiResponse = {
      success: true,
      data: credential
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Error creating 2FA credential:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// PUT /api/credentials/:id/password - Update password credential
router.put('/:id/password', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { error, value } = updatePasswordCredentialSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }

    const request: UpdatePasswordCredentialRequest = value;
    const credential = await credentialService.updatePasswordCredential(id, request);

    const response: ApiResponse = {
      success: true,
      data: credential
    };

    res.json(response);
  } catch (error) {
    console.error('Error updating password credential:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    
    if (error instanceof Error && error.message.includes('not found')) {
      res.status(404).json(response);
    } else {
      res.status(500).json(response);
    }
  }
});

// PUT /api/credentials/:id/2fa - Update 2FA credential
router.put('/:id/2fa', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { error, value } = updateTwoFactorCredentialSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }

    const request: UpdateTwoFactorCredentialRequest = value;
    const credential = await credentialService.updateTwoFactorCredential(id, request);

    const response: ApiResponse = {
      success: true,
      data: credential
    };

    res.json(response);
  } catch (error) {
    console.error('Error updating 2FA credential:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    
    if (error instanceof Error && error.message.includes('not found')) {
      res.status(404).json(response);
    } else {
      res.status(500).json(response);
    }
  }
});

// DELETE /api/credentials/:id - Delete credential
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const deleted = await credentialService.deleteCredential(id);

    if (!deleted) {
      const response: ApiResponse = {
        success: false,
        error: 'Credential not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true
    };

    res.json(response);
  } catch (error) {
    console.error('Error deleting credential:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// POST /api/credentials/:id/test-totp - Test TOTP generation (for 2FA credentials)
router.post('/:id/test-totp', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const code = await credentialService.generateTOTPCode(id);

    if (!code) {
      const response: ApiResponse = {
        success: false,
        error: '2FA credential not found or invalid'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: { code }
    };

    res.json(response);
  } catch (error) {
    console.error('Error generating TOTP code:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

export { router as credentialRoutes };
