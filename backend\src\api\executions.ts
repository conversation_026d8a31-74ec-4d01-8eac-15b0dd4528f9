import { Router, Request, Response } from 'express';
import { 
  FlowExecution, 
  ExecuteFlowRequest, 
  ApiResponse 
} from '@rpa-project/shared';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { ExecutionService } from '../services/executionService';
import { QueueService } from '../queue/queueService';

const router = Router();
const executionService = new ExecutionService();
const queueService = new QueueService();

// GET /api/executions - Get all executions
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const { flowId, status, limit = 50, offset = 0 } = req.query;
  
  const executions = await executionService.getExecutions({
    flowId: flowId as string,
    status: status as any,
    limit: parseInt(limit as string),
    offset: parseInt(offset as string)
  });
  
  const response: ApiResponse<FlowExecution[]> = {
    success: true,
    data: executions
  };
  
  res.json(response);
}));

// GET /api/executions/:id - Get execution by ID
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const execution = await executionService.getExecutionById(id);
  
  if (!execution) {
    throw createError('Execution not found', 404);
  }
  
  const response: ApiResponse<FlowExecution> = {
    success: true,
    data: execution
  };
  
  res.json(response);
}));

// POST /api/executions - Execute flow
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const executeRequest: ExecuteFlowRequest = req.body;
  
  if (!executeRequest.flowId) {
    throw createError('Flow ID is required', 400);
  }
  
  // Create execution record
  const execution = await executionService.createExecution(
    executeRequest.flowId,
    executeRequest.variables || {}
  );
  
  // Add to queue for processing
  await queueService.addFlowExecution(execution.id, {
    flowId: executeRequest.flowId,
    variables: executeRequest.variables || {}
  });
  
  const response: ApiResponse<FlowExecution> = {
    success: true,
    data: execution,
    message: 'Flow execution started'
  };
  
  res.status(201).json(response);
}));

// POST /api/executions/:id/cancel - Cancel execution
router.post('/:id/cancel', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const execution = await executionService.getExecutionById(id);
  if (!execution) {
    throw createError('Execution not found', 404);
  }
  
  if (execution.status === 'completed' || execution.status === 'failed' || execution.status === 'cancelled') {
    throw createError('Cannot cancel execution that is already finished', 400);
  }
  
  // Cancel in queue
  await queueService.cancelExecution(id);
  
  // Update execution status
  await executionService.updateExecutionStatus(id, 'cancelled');
  
  const response: ApiResponse = {
    success: true,
    message: 'Execution cancelled'
  };
  
  res.json(response);
}));

// GET /api/executions/:id/logs - Get execution logs
router.get('/:id/logs', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const execution = await executionService.getExecutionById(id);
  if (!execution) {
    throw createError('Execution not found', 404);
  }
  
  const response: ApiResponse = {
    success: true,
    data: execution.logs
  };
  
  res.json(response);
}));

export { router as executionRoutes };
