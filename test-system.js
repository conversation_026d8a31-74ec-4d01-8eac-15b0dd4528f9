#!/usr/bin/env node

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:3001/api';

async function testSystem() {
  console.log('🧪 Testing RPA System...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing API health...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('✅ API is healthy\n');

    // Test 2: Create a flow
    console.log('2. Creating test flow...');
    const testFlow = JSON.parse(fs.readFileSync('test-flow.json', 'utf8'));
    
    const createResponse = await axios.post(`${API_BASE}/flows`, testFlow);
    const flowId = createResponse.data.data.id;
    console.log(`✅ Flow created with ID: ${flowId}\n`);

    // Test 3: Get the flow
    console.log('3. Retrieving flow...');
    const getResponse = await axios.get(`${API_BASE}/flows/${flowId}`);
    console.log(`✅ Flow retrieved: ${getResponse.data.data.name}\n`);

    // Test 4: List all flows
    console.log('4. Listing all flows...');
    const listResponse = await axios.get(`${API_BASE}/flows`);
    console.log(`✅ Found ${listResponse.data.data.length} flows\n`);

    // Test 5: Execute the flow
    console.log('5. Executing flow...');
    const executeResponse = await axios.post(`${API_BASE}/executions`, {
      flowId: flowId,
      variables: {}
    });
    const executionId = executeResponse.data.data.id;
    console.log(`✅ Flow execution started with ID: ${executionId}\n`);

    // Test 6: Monitor execution
    console.log('6. Monitoring execution...');
    let execution;
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds max

    do {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
      const execResponse = await axios.get(`${API_BASE}/executions/${executionId}`);
      execution = execResponse.data.data;
      attempts++;
      
      console.log(`   Status: ${execution.status} (attempt ${attempts}/${maxAttempts})`);
      
      if (attempts >= maxAttempts) {
        console.log('⚠️  Execution timeout reached');
        break;
      }
    } while (execution.status === 'pending' || execution.status === 'running');

    if (execution.status === 'completed') {
      console.log('✅ Flow execution completed successfully!');
      console.log(`   Results: ${JSON.stringify(execution.results, null, 2)}`);
      console.log(`   Logs: ${execution.logs.length} entries`);
    } else if (execution.status === 'failed') {
      console.log('❌ Flow execution failed');
      console.log(`   Error: ${execution.error}`);
    }

    // Test 7: Queue stats
    console.log('\n7. Checking queue statistics...');
    const queueResponse = await axios.get(`${API_BASE}/queue/stats`);
    console.log(`✅ Queue stats: ${JSON.stringify(queueResponse.data.data, null, 2)}\n`);

    // Test 8: Clean up
    console.log('8. Cleaning up...');
    await axios.delete(`${API_BASE}/flows/${flowId}`);
    console.log('✅ Test flow deleted\n');

    console.log('🎉 All tests passed! System is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Check if we're running this script directly
if (require.main === module) {
  testSystem().catch(console.error);
}

module.exports = { testSystem };
