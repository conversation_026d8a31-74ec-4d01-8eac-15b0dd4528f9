import { useState, useEffect } from 'react'
import { CustomerToken, CreateCustomerTokenRequest, UpdateCustomerTokenRequest } from '@rpa-project/shared'
import { customerApi } from '../../services/api'
import { Portal } from '../ui/Portal'

interface CustomerTokenFormProps {
  customerId: string
  token?: CustomerToken | null
  onSuccess: () => void
  onCancel: () => void
}

export function CustomerTokenForm({ customerId, token, onSuccess, onCancel }: CustomerTokenFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    apiToken: '',
    refreshToken: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const isEditing = !!token

  useEffect(() => {
    if (token) {
      setFormData({
        name: token.name,
        description: token.description || '',
        apiToken: '',
        refreshToken: ''
      })
    }
  }, [token])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      if (isEditing) {
        const updateRequest: UpdateCustomerTokenRequest = {
          name: formData.name || undefined,
          description: formData.description || undefined,
          apiToken: formData.apiToken || undefined,
          refreshToken: formData.refreshToken || undefined
        }
        
        // Remove empty fields
        Object.keys(updateRequest).forEach(key => {
          if (updateRequest[key as keyof UpdateCustomerTokenRequest] === '') {
            delete updateRequest[key as keyof UpdateCustomerTokenRequest]
          }
        })

        const response = await customerApi.updateCustomerToken(customerId, token!.id, updateRequest)
        if (!response.success) {
          throw new Error(response.error || 'Failed to update token')
        }
      } else {
        const createRequest: CreateCustomerTokenRequest = {
          name: formData.name,
          description: formData.description || undefined,
          apiToken: formData.apiToken || undefined,
          refreshToken: formData.refreshToken || undefined
        }

        const response = await customerApi.createCustomerToken(customerId, createRequest)
        if (!response.success) {
          throw new Error(response.error || 'Failed to create token')
        }
      }

      onSuccess()
    } catch (err: any) {
      setError(err.message || 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <Portal>
      <div className="modal-overlay" onClick={onCancel}>
        <div
          className="modal-content"
          style={{ width: '100%', maxWidth: '70vw' }}
          onClick={(e) => e.stopPropagation()}
        >
        {/* Modal Header */}
        <div style={{
          flexShrink: 0,
          borderBottom: '1px solid #e5e7eb',
          margin: 0,
          padding: '1.5rem',
          textAlign: 'left'
        }}>
          <p className="dashboard-title" style={{ fontSize: '1.5rem', margin: 0 }}>
            {isEditing ? 'Redigera token' : 'Ny token'}
          </p>
        </div>

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <div style={{
            flex: 1,
            overflow: 'auto',
            padding: '1.5rem'
          }}>
            {error && (
              <div className="error-card" style={{ marginBottom: '1rem' }}>
                <h3 className="error-title">Fel</h3>
                <p className="error-message">{error}</p>
              </div>
            )}

            <div className="form-group">
              <label className="form-label">
                Tokennamn *
              </label>
              <input
                type="text"
                className="form-input"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder="Ange tokennamn"
                required
                maxLength={100}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                Unikt namn för denna token (max 100 tecken)
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">
                Beskrivning
              </label>
              <textarea
                className="form-input"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Ange beskrivning (valfritt)"
                maxLength={500}
                rows={3}
                style={{ resize: 'vertical', minHeight: '80px' }}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                Beskrivning av vad denna token används för (max 500 tecken)
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">
                API Token
              </label>
              <input
                type="password"
                className="form-input"
                value={formData.apiToken}
                onChange={(e) => handleChange('apiToken', e.target.value)}
                placeholder={isEditing ? "Lämna tomt för att behålla nuvarande" : "Ange API token (valfritt)"}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                {isEditing ? 'Lämna tomt för att behålla nuvarande token' : 'API token för integration'}
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">
                Refresh Token
              </label>
              <input
                type="password"
                className="form-input"
                value={formData.refreshToken}
                onChange={(e) => handleChange('refreshToken', e.target.value)}
                placeholder={isEditing ? "Lämna tomt för att behålla nuvarande" : "Ange refresh token (valfritt)"}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                {isEditing ? 'Lämna tomt för att behålla nuvarande token' : 'Refresh token för automatisk förnyelse'}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div style={{ 
            display: 'flex', 
            gap: '0.75rem', 
            justifyContent: 'flex-end', 
            padding: '1.5rem',
            borderTop: '1px solid #e5e7eb',
            flexShrink: 0
          }}>
            <button
              type="button"
              onClick={onCancel}
              className="action-button secondary"
            >
              <span>Avbryt</span>
            </button>
            <button
              type="submit"
              disabled={loading}
              className={`action-button ${loading ? 'secondary' : 'primary'}`}
              style={{ cursor: loading ? 'not-allowed' : 'pointer' }}
            >
              <span>{loading ? 'Sparar...' : (isEditing ? 'Uppdatera' : 'Skapa')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
    </Portal>
  )
}
