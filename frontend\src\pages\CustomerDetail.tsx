import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Customer, UpdateCustomerRequest } from '@rpa-project/shared'
import { customerApi } from '../services/api'
import { CustomerTokenList } from '../components/customers/CustomerTokenList'

export function CustomerDetail() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    customerNumber: '',
    name: '',
    vismaNumber: ''
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    if (id) {
      loadCustomer()
    }
  }, [id])

  const loadCustomer = async () => {
    if (!id) return

    try {
      setLoading(true)
      setError(null)
      const response = await customerApi.getCustomer(id)
      if (response.success && response.data) {
        setCustomer(response.data)
        setFormData({
          customerNumber: response.data.customerNumber,
          name: response.data.name,
          vismaNumber: response.data.vismaNumber || ''
        })
      } else {
        setError(response.error || 'Misslyckades att ladda kund')
      }
    } catch (err) {
      setError('Misslyckades att ladda kund')
      console.error('Error loading customer:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleEditCustomer = () => {
    setIsEditing(true)
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    if (customer) {
      setFormData({
        customerNumber: customer.customerNumber,
        name: customer.name,
        vismaNumber: customer.vismaNumber || ''
      })
    }
    setError(null)
  }

  const handleSaveCustomer = async () => {
    if (!customer) return

    try {
      setFormLoading(true)
      setError(null)

      const updateRequest: UpdateCustomerRequest = {
        customerNumber: formData.customerNumber || undefined,
        name: formData.name || undefined,
        vismaNumber: formData.vismaNumber || undefined
      }

      // Remove empty fields
      Object.keys(updateRequest).forEach(key => {
        if (updateRequest[key as keyof UpdateCustomerRequest] === '') {
          delete updateRequest[key as keyof UpdateCustomerRequest]
        }
      })

      const response = await customerApi.updateCustomer(customer.id, updateRequest)
      if (response.success) {
        setIsEditing(false)
        loadCustomer()
      } else {
        setError(response.error || 'Misslyckades att uppdatera kund')
      }
    } catch (err) {
      setError('Misslyckades att uppdatera kund')
      console.error('Error updating customer:', err)
    } finally {
      setFormLoading(false)
    }
  }

  const handleDeleteCustomer = async () => {
    if (!customer || !confirm(`Är du säker på att du vill ta bort kunden "${customer.name}"?`)) {
      return
    }

    try {
      const response = await customerApi.deleteCustomer(customer.id)
      if (response.success) {
        navigate('/customers')
      } else {
        setError(response.error || 'Misslyckades att ta bort kund')
      }
    } catch (err) {
      setError('Misslyckades att ta bort kund')
      console.error('Error deleting customer:', err)
    }
  }

  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('sv-SE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar kund...</div>
      </div>
    )
  }

  if (error || !customer) {
    return (
      <div className="dashboard-container">
        <div className="error-card">
          <h3 className="error-title">Fel</h3>
          <p className="error-message">{error || 'Kund hittades inte'}</p>
        </div>
        <div style={{ padding: '1rem' }}>
          <button
            onClick={() => navigate('/customers')}
            className="action-button secondary"
          >
            <span>← Tillbaka till kunder</span>
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">{customer.name}</p>
          <p className="dashboard-subtitle">
            Hantera kundinformation och tokens för {customer.name}.
          </p>
        </div>
        <div style={{ display: 'flex', gap: '0.75rem' }}>
          <button
            onClick={() => navigate('/customers')}
            className="action-button secondary"
          >
            <span>← Tillbaka till kunder</span>
          </button>
          {isEditing ? (
            <>
              <button
                onClick={handleSaveCustomer}
                disabled={formLoading}
                className="action-button primary"
              >
                <span>{formLoading ? 'Sparar...' : 'Spara'}</span>
              </button>
              <button
                onClick={handleCancelEdit}
                disabled={formLoading}
                className="action-button secondary"
              >
                <span>Avbryt</span>
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleEditCustomer}
                className="action-button secondary"
              >
                <span>Redigera kund</span>
              </button>
              <button
                onClick={handleDeleteCustomer}
                className="action-button danger"
              >
                <span>Ta bort kund</span>
              </button>
            </>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-card">
          <h3 className="error-title">Fel</h3>
          <p className="error-message">{error}</p>
        </div>
      )}

      {/* Customer Information Section */}
      <h2 className="section-title">Kundinformation</h2>
      <div className="table-container">
        <div className="form-container">
          <div style={{ padding: '1.5rem' }}>
            <div className="responsive-form-grid">
              <div className="form-group">
                <label className="form-label">
                  Kundnummer *
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    className="form-input"
                    value={formData.customerNumber}
                    onChange={(e) => handleFormChange('customerNumber', e.target.value)}
                    placeholder="Ange kundnummer"
                    required
                    maxLength={50}
                  />
                ) : (
                  <div className="form-display-value">{customer.customerNumber}</div>
                )}
                {isEditing && (
                  <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                    Unikt kundnummer (max 50 tecken)
                  </div>
                )}
              </div>

              <div className="form-group">
                <label className="form-label">
                  Kundnamn *
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    className="form-input"
                    value={formData.name}
                    onChange={(e) => handleFormChange('name', e.target.value)}
                    placeholder="Ange kundnamn"
                    required
                    maxLength={200}
                  />
                ) : (
                  <div className="form-display-value">{customer.name}</div>
                )}
                {isEditing && (
                  <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                    Kundnamn (max 200 tecken)
                  </div>
                )}
              </div>

              <div className="form-group">
                <label className="form-label">
                  Visma-nummer
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    className="form-input"
                    value={formData.vismaNumber}
                    onChange={(e) => handleFormChange('vismaNumber', e.target.value)}
                    placeholder="Ange Visma-nummer (valfritt)"
                    maxLength={50}
                  />
                ) : (
                  <div className="form-display-value">{customer.vismaNumber || '—'}</div>
                )}
                {isEditing && (
                  <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                    Valfritt Visma-nummer (max 50 tecken)
                  </div>
                )}
              </div>

              <div className="form-group">
                <label className="form-label">
                  Skapad
                </label>
                <div className="form-display-value">{formatDate(customer.createdAt)}</div>
              </div>

              <div className="form-group">
                <label className="form-label">
                  Senast uppdaterad
                </label>
                <div className="form-display-value">{formatDate(customer.updatedAt)}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Customer Token Management */}
      <CustomerTokenList
        customerId={customer.id}
        customerName={customer.name}
      />
    </div>
  )
}
