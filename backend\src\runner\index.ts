/**
 * RPA Runner System - Multi-Engine Execution Architecture
 *
 * This module provides a flexible, extensible system for executing RPA flows
 * using different execution engines based on step types.
 */

import { getRunnerFactory } from './RunnerFactory';
import { RunnerType, getImplementedRunnerTypes } from './stepTypes';

// Core interfaces and base classes
export { IR<PERSON><PERSON>, BaseRunner, RunnerContext, StepExecutionResult } from './IRunner';

// Step type categorization and mapping
export {
  StepCategory,
  RunnerType,
  WEB_AUTOMATION_STEPS,
  API_STEPS,
  DATABASE_STEPS,
  FILE_SYSTEM_STEPS,
  StepType,
  STEP_TO_RUNNER_MAP,
  STEP_TO_CATEGORY_MAP,
  getRunnerTypeForStep,
  getCategoryForStep,
  isStepTypeSupported,
  getStepTypesForRunner,
  getStepTypesForCategory,
  isRunnerImplemented,
  getImplementedRunnerTypes,
  getAllRunnerTypes
} from './stepTypes';

// Runner registry and management
export {
  RunnerRegistry,
  RunnerConstructor,
  getRunnerRegistry
} from './RunnerRegistry';

// Runner factory
export {
  RunnerFactory,
  RunnerFactoryConfig,
  getRunnerFactory
} from './RunnerFactory';

// Flow executor
export { FlowExecutor } from './FlowExecutor';

// Specific runner implementations
export { PlaywrightRunner } from './playwrightRunner';

// Legacy exports for backward compatibility (if needed)
export { PlaywrightRunnerContext } from './playwrightRunner';

/**
 * Initialize the runner system with default configurations
 * This should be called during application startup
 */
export function initializeRunnerSystem(): void {
  const factory = getRunnerFactory();
  console.log('🔧 Runner system initialized');
  console.log('📊 Factory stats:', factory.getStats());
}

/**
 * Get system information about available runners and supported steps
 */
export function getRunnerSystemInfo(): {
  supportedStepTypes: string[];
  implementedRunners: RunnerType[];
  factoryStats: any;
} {
  const factory = getRunnerFactory();

  return {
    supportedStepTypes: factory.getSupportedStepTypes(),
    implementedRunners: getImplementedRunnerTypes(),
    factoryStats: factory.getStats()
  };
}

/**
 * Validate that a flow can be executed with the current runner system
 */
export function validateFlowCompatibility(flow: any): {
  isCompatible: boolean;
  unsupportedSteps: string[];
  requiredRunners: RunnerType[];
} {
  const factory = getRunnerFactory();
  const analysis = factory.analyzeFlow(flow);

  return {
    isCompatible: analysis.unsupportedSteps.length === 0,
    unsupportedSteps: analysis.unsupportedSteps,
    requiredRunners: analysis.requiredRunners
  };
}
