import { useState, useEffect } from 'react'
import { RpaStep, validateStep, Credential } from '@rpa-project/shared'
import { ElementSelector } from './ElementSelector'
import { credentialApi } from '../../services/api'
import { useScrollFade } from '../../hooks/useScrollFade'
import { VariableInput } from './VariableInput'

interface StepEditorProps {
  step: RpaStep
  onSave: (step: RpaStep) => void
  onCancel: () => void
  compact?: boolean
  steps?: RpaStep[]
  currentStepIndex?: number
}

export function StepEditor({ step, onSave, onCancel, compact = false, steps = [], currentStepIndex = 0 }: StepEditorProps) {
  const [editedStep, setEditedStep] = useState<RpaStep>({ ...step })
  const [errors, setErrors] = useState<string[]>([])
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [loadingCredentials, setLoadingCredentials] = useState(false)
  const { containerRef: modalRef, overlayRef: modalOverlayRef, fadeOverlayStyle: modalFadeStyle } = useScrollFade({ fadeColor: 'white' })

  useEffect(() => {
    if (editedStep.type === 'fillPassword' || editedStep.type === 'fill2FA') {
      loadCredentials()
    }
  }, [editedStep.type])

  const loadCredentials = async () => {
    try {
      setLoadingCredentials(true)
      const response = await credentialApi.getCredentials()
      if (response.success && response.data) {
        setCredentials(response.data)
      }
    } catch (error) {
      console.error('Error loading credentials:', error)
    } finally {
      setLoadingCredentials(false)
    }
  }

  const handleSave = () => {
    const validation = validateStep(editedStep)
    if (!validation.valid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep)
  }

  const updateStep = (updates: Partial<RpaStep>) => {
    setEditedStep(prev => ({ ...prev, ...updates } as RpaStep))
  }

  const renderFormField = (label: string, children: React.ReactNode, fullWidth = false) => (
    <div style={{
      marginBottom: compact ? '0.75rem' : '1rem',
      flex: compact && !fullWidth ? '1 1 300px' : '1 1 100%',
      minWidth: compact && !fullWidth ? '250px' : 'auto'
    }}>
      <label className="form-label" style={{
        fontSize: compact ? '0.75rem' : '0.875rem',
        marginBottom: compact ? '0.375rem' : '0.5rem'
      }}>{label}</label>
      {children}
    </div>
  )

  const inputStyle = {
    padding: compact ? '0.375rem 0.5rem' : '0.5rem 0.75rem',
    fontSize: compact ? '0.75rem' : '0.875rem'
  }

  const selectStyle = {
    ...inputStyle,
    backgroundColor: 'white'
  }

  const renderStepFields = () => {
    switch (editedStep.type) {
      case 'navigate':
        return (
          <>
            {renderFormField('URL',
              <VariableInput
                type="url"
                value={editedStep.url || ''}
                onChange={(value) => updateStep({ url: value })}
                placeholder="https://example.com"
                steps={steps}
                currentStepIndex={currentStepIndex}
                style={inputStyle}
              />,
              true
            )}
            {renderFormField('Vänta tills',
              <select
                className="form-input"
                style={selectStyle}
                value={editedStep.waitUntil || 'load'}
                onChange={(e) => updateStep({ waitUntil: e.target.value as any })}
              >
                <option value="load">Sidan laddad</option>
                <option value="domcontentloaded">DOM innehåll laddat</option>
                <option value="networkidle">Nätverk inaktivt</option>
              </select>
            )}
          </>
        )

      case 'click':
        return (
          <>
            {renderFormField('Element Selector',
              <ElementSelector
                value={editedStep.selector || ''}
                onChange={(selector) => updateStep({ selector })}
                placeholder="button, #id, .class"
                style={inputStyle}
              />
            )}
            {renderFormField('Musknapp',
              <select
                className="form-input"
                style={selectStyle}
                value={editedStep.button || 'left'}
                onChange={(e) => updateStep({ button: e.target.value as any })}
              >
                <option value="left">Vänster</option>
                <option value="right">Höger</option>
                <option value="middle">Mitten</option>
              </select>
            )}
          </>
        )

      case 'fill':
        return (
          <>
            {renderFormField('Element Selector',
              <VariableInput
                value={editedStep.selector || ''}
                onChange={(value) => updateStep({ selector: value })}
                placeholder="input, textarea, [name='field']"
                steps={steps}
                currentStepIndex={currentStepIndex}
                style={inputStyle}
              />,
              true
            )}
            {renderFormField('Value',
              <VariableInput
                value={editedStep.value || ''}
                onChange={(value) => updateStep({ value: value })}
                placeholder="Text to fill"
                steps={steps}
                currentStepIndex={currentStepIndex}
                style={inputStyle}
              />
            )}
          </>
        )

      case 'type':
        return (
          <>
            {renderFormField('Element Selector',
              <VariableInput
                value={editedStep.selector || ''}
                onChange={(value) => updateStep({ selector: value })}
                placeholder="input, textarea"
                steps={steps}
                currentStepIndex={currentStepIndex}
                style={inputStyle}
              />
            )}
            {renderFormField('Text',
              <VariableInput
                value={editedStep.text || ''}
                onChange={(value) => updateStep({ text: value })}
                placeholder="Text to type"
                steps={steps}
                currentStepIndex={currentStepIndex}
                style={inputStyle}
              />
            )}
            {renderFormField('Delay (ms)',
              <input
                type="number"
                style={inputStyle}
                value={editedStep.delay || 0}
                onChange={(e) => updateStep({ delay: parseInt(e.target.value) || 0 })}
                min="0"
              />
            )}
          </>
        )

      case 'selectOption':
        return (
          <>
            {renderFormField('Element Selector',
              <ElementSelector
                value={editedStep.selector || ''}
                onChange={(selector) => updateStep({ selector })}
                placeholder="select, #dropdown-id"
                style={inputStyle}
              />
            )}
            {renderFormField('Selection Method',
              <select
                style={selectStyle}
                value={
                  editedStep.value ? 'value' :
                  editedStep.label ? 'label' :
                  editedStep.index !== undefined ? 'index' : 'value'
                }
                onChange={(e) => {
                  const method = e.target.value
                  if (method === 'value') {
                    updateStep({ value: '', label: undefined, index: undefined })
                  } else if (method === 'label') {
                    updateStep({ value: undefined, label: '', index: undefined })
                  } else if (method === 'index') {
                    updateStep({ value: undefined, label: undefined, index: 0 })
                  }
                }}
              >
                <option value="value">By Value</option>
                <option value="label">By Label</option>
                <option value="index">By Index</option>
              </select>
            )}
            {(editedStep.value !== undefined || (!editedStep.label && editedStep.index === undefined)) &&
              renderFormField('Option Value',
                <input
                  type="text"
                  style={inputStyle}
                  value={editedStep.value || ''}
                  onChange={(e) => updateStep({ value: e.target.value })}
                  placeholder="option-value"
                />
              )
            }
            {editedStep.label !== undefined &&
              renderFormField('Option Label',
                <input
                  type="text"
                  style={inputStyle}
                  value={editedStep.label || ''}
                  onChange={(e) => updateStep({ label: e.target.value })}
                  placeholder="Option Text"
                />
              )
            }
            {editedStep.index !== undefined &&
              renderFormField('Option Index',
                <input
                  type="number"
                  style={inputStyle}
                  value={editedStep.index || 0}
                  onChange={(e) => updateStep({ index: parseInt(e.target.value) || 0 })}
                  min="0"
                  placeholder="0"
                />
              )
            }
          </>
        )

      case 'waitForSelector':
        return (
          <>
            {renderFormField('Element Selector',
              <ElementSelector
                value={editedStep.selector || ''}
                onChange={(selector) => updateStep({ selector })}
                placeholder="Element selector to wait for"
                style={inputStyle}
              />
            )}
            {renderFormField('Tillstånd',
              <select
                className="form-input"
                style={selectStyle}
                value={editedStep.state || 'visible'}
                onChange={(e) => updateStep({ state: e.target.value as any })}
              >
                <option value="visible">Synlig</option>
                <option value="hidden">Dold</option>
                <option value="attached">Bifogad</option>
                <option value="detached">Frånkopplad</option>
              </select>
            )}
          </>
        )

      case 'waitForTimeout':
        return (
          renderFormField('Duration (ms)',
            <input
              type="number"
              style={inputStyle}
              value={editedStep.duration || 1000}
              onChange={(e) => updateStep({ duration: parseInt(e.target.value) || 1000 })}
              min="0"
            />
          )
        )

      case 'extractText':
        return (
          <>
            {renderFormField('Element Selector',
              <ElementSelector
                value={editedStep.selector || ''}
                onChange={(selector) => updateStep({ selector })}
                placeholder="Element to extract text from"
                style={inputStyle}
              />
            )}
            {renderFormField('Variable Name',
              <input
                type="text"
                style={inputStyle}
                value={editedStep.variableName || ''}
                onChange={(e) => updateStep({ variableName: e.target.value })}
                placeholder="Variable to store the text"
              />
            )}
          </>
        )

      case 'takeScreenshot':
        return (
          <>
            {renderFormField('File Path (optional)',
              <input
                type="text"
                style={inputStyle}
                value={editedStep.path || ''}
                onChange={(e) => updateStep({ path: e.target.value })}
                placeholder="screenshots/my-screenshot.png"
              />
            )}
            {renderFormField('Variable Name (for base64)',
              <input
                type="text"
                style={inputStyle}
                value={editedStep.variableName || ''}
                onChange={(e) => updateStep({ variableName: e.target.value })}
                placeholder="screenshotBase64"
              />
            )}
            {renderFormField('',
              <label style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: compact ? '0.75rem' : '0.875rem',
                color: '#374151'
              }}>
                <input
                  type="checkbox"
                  checked={editedStep.fullPage || false}
                  onChange={(e) => updateStep({ fullPage: e.target.checked })}
                />
                Full Page Screenshot
              </label>
            )}
          </>
        )

      case 'fillPassword':
        const passwordCredentials = credentials.filter(c => c.type === 'password')
        return (
          <>
            {renderFormField('Element Selector',
              <ElementSelector
                value={editedStep.selector || ''}
                onChange={(selector) => updateStep({ selector })}
                placeholder="input[type='password'], #password"
                style={inputStyle}
              />
            )}
            {renderFormField('Credential',
              loadingCredentials ? (
                <div style={{ ...inputStyle, color: '#6b7280' }}>Loading credentials...</div>
              ) : (
                <select
                  style={selectStyle}
                  value={editedStep.credentialId || ''}
                  onChange={(e) => updateStep({ credentialId: e.target.value })}
                >
                  <option value="">Select a password credential</option>
                  {passwordCredentials.map((credential) => (
                    <option key={credential.id} value={credential.id}>
                      {credential.name} ({(credential as any).username})
                    </option>
                  ))}
                </select>
              )
            )}
            {passwordCredentials.length === 0 && !loadingCredentials && (
              <div style={{ fontSize: '0.75rem', color: '#dc2626', marginTop: '0.25rem' }}>
                No password credentials found. Create one in the Credentials page.
              </div>
            )}
          </>
        )

      case 'fill2FA':
        const twoFactorCredentials = credentials.filter(c => c.type === '2fa')
        return (
          <>
            {renderFormField('Element Selector',
              <ElementSelector
                value={editedStep.selector || ''}
                onChange={(selector) => updateStep({ selector })}
                placeholder="input, #totp-code"
                style={inputStyle}
              />
            )}
            {renderFormField('Credential',
              loadingCredentials ? (
                <div style={{ ...inputStyle, color: '#6b7280' }}>Loading credentials...</div>
              ) : (
                <select
                  style={selectStyle}
                  value={editedStep.credentialId || ''}
                  onChange={(e) => updateStep({ credentialId: e.target.value })}
                >
                  <option value="">Select a 2FA credential</option>
                  {twoFactorCredentials.map((credential) => (
                    <option key={credential.id} value={credential.id}>
                      {credential.name}
                    </option>
                  ))}
                </select>
              )
            )}
            {twoFactorCredentials.length === 0 && !loadingCredentials && (
              <div style={{ fontSize: '0.75rem', color: '#dc2626', marginTop: '0.25rem' }}>
                No 2FA credentials found. Create one in the Credentials page.
              </div>
            )}
          </>
        )

      case 'conditionalClick':
        return (
          <>
            {renderFormField('Element Selector',
              <ElementSelector
                value={editedStep.selector || ''}
                onChange={(selector) => updateStep({ selector })}
                placeholder="button, #id, .class"
                style={inputStyle}
              />
            )}
            {renderFormField('Villkor',
              <select
                className="form-input"
                style={selectStyle}
                value={editedStep.condition || 'exists'}
                onChange={(e) => updateStep({ condition: e.target.value as any })}
              >
                <option value="exists">Element finns</option>
                <option value="enabled">Element är aktiverat</option>
                <option value="disabled">Element är inaktiverat</option>
              </select>
            )}
            {renderFormField('Åtgärd',
              <select
                className="form-input"
                style={selectStyle}
                value={editedStep.clickIfTrue ? 'true' : 'false'}
                onChange={(e) => updateStep({ clickIfTrue: e.target.value === 'true' })}
              >
                <option value="true">Klicka om villkor är sant</option>
                <option value="false">Klicka om villkor är falskt</option>
              </select>
            )}
            {renderFormField('Musknapp',
              <select
                className="form-input"
                style={selectStyle}
                value={editedStep.button || 'left'}
                onChange={(e) => updateStep({ button: e.target.value as any })}
              >
                <option value="left">Vänster</option>
                <option value="right">Höger</option>
                <option value="middle">Mitten</option>
              </select>
            )}
          </>
        )

      case 'downloadFile':
        return (
          <>
            {renderFormField('Trigger Selector (valfritt)',
              <ElementSelector
                value={editedStep.triggerSelector || ''}
                onChange={(selector) => updateStep({ triggerSelector: selector })}
                placeholder="a[download], button"
                style={inputStyle}
              />
            )}
            {renderFormField('Filnamn (valfritt)',
              <VariableInput
                value={editedStep.filename || ''}
                onChange={(value) => updateStep({ filename: value })}
                placeholder="document.pdf"
                steps={steps}
                currentStepIndex={currentStepIndex}
                style={inputStyle}
              />
            )}
            {renderFormField('Variabelnamn för base64 (valfritt)',
              <input
                type="text"
                style={inputStyle}
                value={editedStep.variableName || ''}
                onChange={(e) => updateStep({ variableName: e.target.value })}
                placeholder="fileContent"
              />
            )}
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginTop: '1rem' }}>
              <input
                type="checkbox"
                id="saveToFile"
                checked={editedStep.saveToFile === true}
                onChange={(e) => updateStep({ saveToFile: e.target.checked })}
              />
              <label htmlFor="saveToFile" style={{ fontSize: '0.875rem', color: '#374151' }}>
                Spara fil till disk
              </label>
            </div>
          </>
        )

      default:
        return (
          <div className="text-gray-500">
            No specific configuration available for this step type.
          </div>
        )
    }
  }

  if (compact) {
    return (
      <div>
        {/* Header with step type */}
        <div style={{
          marginBottom: '1rem',
          paddingBottom: '0.75rem',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <h4 style={{
            margin: 0,
            fontSize: '1rem',
            fontWeight: '500',
            color: '#1a0f0f'
          }}>
            Konfigurera {editedStep.type}-steg
          </h4>
        </div>

        {errors.length > 0 && (
          <div style={{
            marginBottom: '1rem',
            padding: '0.75rem',
            backgroundColor: '#fef2f2',
            border: '1px solid #fecaca',
            borderRadius: '0.5rem',
            fontSize: '0.875rem'
          }}>
            <ul style={{ margin: 0, paddingLeft: '1rem', color: '#991b1b' }}>
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Form fields in responsive columns */}
        <div style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '1rem',
          marginBottom: '1.5rem'
        }}>
          {renderStepFields()}

          {/* Common fields */}
          {renderFormField('Beskrivning (valfritt)',
            <textarea
              className="form-input"
              style={{
                ...inputStyle,
                minHeight: '60px',
                resize: 'vertical'
              }}
              value={editedStep.description || ''}
              onChange={(e) => updateStep({ description: e.target.value })}
              placeholder="Valfri beskrivning för detta steg"
            />,
            true
          )}

          {renderFormField('Timeout (ms)',
            <input
              type="number"
              className="form-input"
              style={inputStyle}
              value={editedStep.timeout || 30000}
              onChange={(e) => updateStep({ timeout: parseInt(e.target.value) || 30000 })}
              min="1000"
              max="300000"
              placeholder="30000"
            />
          )}
        </div>

        {/* Action buttons */}
        <div style={{
          display: 'flex',
          gap: '0.75rem',
          justifyContent: 'flex-end',
          paddingTop: '1rem',
          borderTop: '1px solid #e5e7eb'
        }}>
          <button
            onClick={onCancel}
            className="action-button-small secondary"
          >
            Avbryt
          </button>
          <button
            onClick={handleSave}
            className="action-button-small primary"
          >
            Spara
          </button>
        </div>
      </div>
    )
  }

  // Original modal layout for non-compact mode
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div style={{ position: 'relative', maxWidth: '28rem', width: '100%', margin: '0 1rem', maxHeight: '80vh' }}>
        <div
          ref={modalRef}
          className="bg-white rounded-lg shadow-xl overflow-y-auto custom-scrollbar"
          style={{ height: '100%' }}
        >
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Edit {editedStep.type} Step
          </h3>

          {errors.length > 0 && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
              <ul className="text-sm text-red-700">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="space-y-4">
            <div className="form-group">
              <label className="form-label">Description (optional)</label>
              <input
                type="text"
                className="form-input"
                value={editedStep.description || ''}
                onChange={(e) => updateStep({ description: e.target.value })}
                placeholder="Step description"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Timeout (ms)</label>
              <input
                type="number"
                className="form-input"
                value={editedStep.timeout || 30000}
                onChange={(e) => updateStep({ timeout: parseInt(e.target.value) || 30000 })}
                min="1000"
                max="300000"
              />
            </div>

            {renderStepFields()}
          </div>

          <div className="flex gap-2 mt-6">
            <button
              onClick={handleSave}
              className="btn btn-primary flex-1"
            >
              Save Changes
            </button>
            <button
              onClick={onCancel}
              className="btn btn-outline flex-1"
            >
              Cancel
            </button>
          </div>
        </div>
        </div>
        <div ref={modalOverlayRef} style={modalFadeStyle} />
      </div>
    </div>
  )
}
