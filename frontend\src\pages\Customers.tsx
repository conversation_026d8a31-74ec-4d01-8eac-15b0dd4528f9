import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Customer } from '@rpa-project/shared'
import { customerApi } from '../services/api'
import { CustomerForm } from '../components/customers/CustomerForm'

export function Customers() {
  const navigate = useNavigate()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)

  useEffect(() => {
    loadCustomers()
  }, [])

  const loadCustomers = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await customerApi.getCustomers()
      if (response.success && response.data) {
        setCustomers(response.data)
      } else {
        setError(response.error || 'Misslyckades att ladda kunder')
      }
    } catch (err) {
      setError('Misslyckades att ladda kunder')
      console.error('Error loading customers:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCustomer = () => {
    setEditingCustomer(null)
    setShowForm(true)
  }

  const handleEditCustomer = (customer: Customer) => {
    navigate(`/customers/${customer.id}`)
  }

  const handleDeleteCustomer = async (id: string) => {
    if (!confirm('Är du säker på att du vill ta bort denna kund?')) {
      return
    }

    try {
      const response = await customerApi.deleteCustomer(id)
      if (response.success) {
        await loadCustomers()
      } else {
        setError(response.error || 'Misslyckades att ta bort kund')
      }
    } catch (err) {
      setError('Misslyckades att ta bort kund')
      console.error('Error deleting customer:', err)
    }
  }

  const handleFormSuccess = (customerId?: string) => {
    setShowForm(false)
    setEditingCustomer(null)

    if (customerId) {
      // Navigate to the newly created customer's detail page
      navigate(`/customers/${customerId}`)
    } else {
      // For updates, just reload the customers list
      loadCustomers()
    }
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingCustomer(null)
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('sv-SE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar kunder...</div>
      </div>
    )
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Kunder</p>
          <p className="dashboard-subtitle">
            Hantera kundinformation och kundnummer.
          </p>
        </div>
        <button
          onClick={handleCreateCustomer}
          className="action-button primary"
        >
          <span>Skapa kund</span>
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-card">
          <h3 className="error-title">Fel vid laddning av kunder</h3>
          <p className="error-message">{error}</p>
        </div>
      )}

      {/* Customers List */}
      {customers.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">👥</div>
            <p className="empty-state-title">Inga kunder än</p>
            <p className="empty-state-subtitle">Skapa din första kund för att komma igång med kundhantering</p>
            <button
              onClick={handleCreateCustomer}
              className="action-button primary"
            >
              <span>Skapa första kund</span>
            </button>
          </div>
        </div>
      ) : (
        <>
          <h2 className="section-title">Dina kunder</h2>
          <div className="table-container">
            <div className="activity-table">
              <table className="table">
                <thead>
                  <tr>
                    <th>Kund</th>
                    <th>Visma-nummer</th>
                    <th>Skapad</th>
                    <th>Åtgärder</th>
                  </tr>
                </thead>
                <tbody>
                  {customers.map((customer) => (
                    <tr key={customer.id}>
                      <td>
                        <div style={{ cursor: 'pointer' }} onClick={() => handleEditCustomer(customer)}>
                          <div className="flow-name">{customer.name}</div>
                          <div className="flow-description-small">{customer.customerNumber}</div>
                        </div>
                      </td>
                      <td className="secondary-text">
                        {customer.vismaNumber || '—'}
                      </td>
                      <td className="secondary-text">
                        {formatDate(customer.createdAt)}
                      </td>
                      <td>
                        <div className="flow-actions">
                          <button
                            onClick={() => handleEditCustomer(customer)}
                            className="action-button-small icon-only"
                            title="Redigera kund"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDeleteCustomer(customer.id)}
                            className="action-button-small danger"
                            title="Ta bort kund"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <polyline points="3,6 5,6 21,6"></polyline>
                              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                              <line x1="10" y1="11" x2="10" y2="17"></line>
                              <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}

      {/* Customer form modal */}
      {showForm && (
        <CustomerForm
          customer={editingCustomer}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      )}
    </div>
  )
}
