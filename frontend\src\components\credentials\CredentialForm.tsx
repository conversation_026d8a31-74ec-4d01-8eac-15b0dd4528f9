import { useState, useEffect } from 'react'
import { 
  Credential, 
  CreatePasswordCredentialRequest, 
  CreateTwoFactorCredentialRequest,
  UpdatePasswordCredentialRequest,
  UpdateTwoFactorCredentialRequest
} from '@rpa-project/shared'
import { credentialApi } from '../../services/api'
import { Portal } from '../ui/Portal'

interface CredentialFormProps {
  credential?: Credential | null
  onSuccess: () => void
  onCancel: () => void
}

export function CredentialForm({ credential, onSuccess, onCancel }: CredentialFormProps) {
  const [credentialType, setCredentialType] = useState<'password' | '2fa'>('password')
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [secret, setSecret] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [testingTOTP, setTestingTOTP] = useState(false)
  const [totpCode, setTotpCode] = useState<string | null>(null)

  const isEditing = !!credential

  useEffect(() => {
    if (credential) {
      setCredentialType(credential.type)
      setName(credential.name)
      setDescription(credential.description || '')
      if (credential.type === 'password') {
        setUsername((credential as any).username || '')
      }
    }
  }, [credential])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      if (credentialType === 'password') {
        if (isEditing) {
          const request: UpdatePasswordCredentialRequest = {
            name: name || undefined,
            description: description || undefined,
            username: username || undefined,
            password: password || undefined
          }
          await credentialApi.updatePasswordCredential(credential!.id, request)
        } else {
          const request: CreatePasswordCredentialRequest = {
            name,
            description,
            username,
            password
          }
          await credentialApi.createPasswordCredential(request)
        }
      } else {
        if (isEditing) {
          const request: UpdateTwoFactorCredentialRequest = {
            name: name || undefined,
            description: description || undefined,
            secret: secret || undefined
          }
          await credentialApi.updateTwoFactorCredential(credential!.id, request)
        } else {
          const request: CreateTwoFactorCredentialRequest = {
            name,
            description,
            secret
          }
          await credentialApi.createTwoFactorCredential(request)
        }
      }

      onSuccess()
    } catch (err) {
      setError('Failed to save credential')
      console.error('Error saving credential:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleTestTOTP = async () => {
    if (!credential || credential.type !== '2fa') return

    setTestingTOTP(true)
    try {
      const response = await credentialApi.testTOTP(credential.id)
      if (response.success && response.data) {
        setTotpCode(response.data.code)
        setTimeout(() => setTotpCode(null), 5000) // Clear after 5 seconds
      } else {
        setError('Failed to generate TOTP code')
      }
    } catch (err) {
      setError('Failed to generate TOTP code')
      console.error('Error testing TOTP:', err)
    } finally {
      setTestingTOTP(false)
    }
  }

  return (
    <Portal>
      <div className="modal-overlay" onClick={onCancel}>
        <div
          className="modal-content"
          style={{ width: '100%', maxWidth: '70vw' }}
          onClick={(e) => e.stopPropagation()}
        >
        {/* Modal Header */}
        <div style={{
          flexShrink: 0,
          borderBottom: '1px solid #e5e7eb',
          margin: 0,
          padding: '1.5rem',
          textAlign: 'left'
        }}>
          <p className="dashboard-title" style={{ fontSize: '1.5rem', margin: 0 }}>
            {isEditing ? 'Redigera inloggningsuppgift' : 'Ny inloggningsuppgift'}
          </p>
        </div>

        <div style={{ flex: 1, overflow: 'auto', padding: '1.5rem' }}>

          {error && (
            <div className="error-card" style={{ marginBottom: '1rem' }}>
              <h3 className="error-title">Fel</h3>
              <p className="error-message">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Credential Type (only for new credentials) */}
            {!isEditing && (
              <div className="form-group">
                <label className="form-label">
                  Typ av inloggningsuppgift
                </label>
                <div className="choice-buttons">
                  <label className={`choice-button ${credentialType === 'password' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      value="password"
                      checked={credentialType === 'password'}
                      onChange={(e) => setCredentialType(e.target.value as 'password')}
                    />
                    <span>🔑 Användarnamn & Lösenord</span>
                  </label>
                  <label className={`choice-button ${credentialType === '2fa' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      value="2fa"
                      checked={credentialType === '2fa'}
                      onChange={(e) => setCredentialType(e.target.value as '2fa')}
                    />
                    <span>🔐 2FA Token</span>
                  </label>
                </div>
              </div>
            )}

            {/* Name */}
            <div className="form-group">
              <label className="form-label">
                Namn *
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="form-input"
                placeholder="t.ex. Gmail-inloggning, Bankkonto"
              />
            </div>

            {/* Description */}
            <div className="form-group">
              <label className="form-label">
                Beskrivning
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={2}
                className="form-input form-textarea"
                placeholder="Valfri beskrivning"
              />
            </div>

            {/* Password Credential Fields */}
            {credentialType === 'password' && (
              <>
                <div className="form-group">
                  <label className="form-label">
                    Användarnamn *
                  </label>
                  <input
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    className="form-input"
                    placeholder="Användarnamn eller e-post"
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">
                    Lösenord {isEditing ? '(lämna tomt för att behålla nuvarande)' : '*'}
                  </label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required={!isEditing}
                    className="form-input"
                    placeholder="Lösenord"
                  />
                </div>
              </>
            )}

            {/* 2FA Credential Fields */}
            {credentialType === '2fa' && (
              <div className="form-group">
                <label className="form-label">
                  Hemlig nyckel {isEditing ? '(lämna tomt för att behålla nuvarande)' : '*'}
                </label>
                <input
                  type="text"
                  value={secret}
                  onChange={(e) => setSecret(e.target.value)}
                  required={!isEditing}
                  className="form-input"
                  style={{ fontFamily: 'monospace' }}
                  placeholder="Base32 hemlig nyckel (t.ex. JBSWY3DPEHPK3PXP)"
                />
                <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  Ange base32 hemlig nyckel från din autentiseringsapp
                </div>

                {/* Test TOTP button for editing */}
                {isEditing && credential?.type === '2fa' && (
                  <div style={{ marginTop: '0.5rem' }}>
                    <button
                      type="button"
                      onClick={handleTestTOTP}
                      disabled={testingTOTP}
                      className="action-button-small secondary"
                    >
                      <span>{testingTOTP ? 'Genererar...' : 'Testa TOTP'}</span>
                    </button>
                    {totpCode && (
                      <div style={{
                        marginTop: '0.5rem',
                        padding: '0.5rem',
                        backgroundColor: '#f0fdf4',
                        border: '1px solid #bbf7d0',
                        borderRadius: '0.375rem',
                        fontSize: '0.875rem',
                        color: '#166534'
                      }}>
                        Nuvarande TOTP-kod: <strong>{totpCode}</strong>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Actions */}
            <div style={{ display: 'flex', gap: '0.75rem', justifyContent: 'flex-end', marginTop: '2rem', paddingTop: '1rem', borderTop: '1px solid #e5e7eb' }}>
              <button
                type="button"
                onClick={onCancel}
                className="action-button secondary"
              >
                <span>Avbryt</span>
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`action-button ${loading ? 'secondary' : 'primary'}`}
                style={{ cursor: loading ? 'not-allowed' : 'pointer' }}
              >
                <span>{loading ? 'Sparar...' : (isEditing ? 'Uppdatera' : 'Skapa')}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    </Portal>
  )
}
