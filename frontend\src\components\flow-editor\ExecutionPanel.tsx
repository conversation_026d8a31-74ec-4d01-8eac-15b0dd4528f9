import { useState, useEffect, useRef } from 'react'
import { FlowExecution, ExecutionLog } from '@rpa-project/shared'
import { executionApi } from '../../services/api'
import { Portal } from '../ui/Portal'

interface ExecutionPanelProps {
  isOpen: boolean
  onClose: () => void
  execution: FlowExecution | null
}

export function ExecutionPanel({ isOpen, onClose, execution }: ExecutionPanelProps) {
  const [logs, setLogs] = useState<ExecutionLog[]>([])
  const [loading, setLoading] = useState(false)
  const [cancelling, setCancelling] = useState(false)
  const logsEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new logs arrive
  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [logs])

  // Poll for logs when execution is running
  useEffect(() => {
    if (!execution || !isOpen) {
      setLogs([])
      return
    }

    const loadLogs = async () => {
      try {
        setLoading(true)
        const response = await executionApi.getExecutionLogs(execution.id)
        if (response.success) {
          setLogs(response.data || [])
        }
      } catch (error) {
        console.error('Failed to load logs:', error)
      } finally {
        setLoading(false)
      }
    }

    // Load initial logs
    loadLogs()

    // Poll for updates if execution is still running
    const interval = setInterval(() => {
      if (execution.status === 'running' || execution.status === 'pending') {
        loadLogs()
      }
    }, 1000) // Poll every second

    return () => clearInterval(interval)
  }, [execution, isOpen])

  const handleCancel = async () => {
    if (!execution || cancelling) return

    try {
      setCancelling(true)
      await executionApi.cancelExecution(execution.id)
      // The execution status will be updated through polling
    } catch (error) {
      console.error('Failed to cancel execution:', error)
      alert('Failed to cancel execution')
    } finally {
      setCancelling(false)
    }
  }

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error': return '#ef4444'
      case 'warn': return '#f59e0b'
      case 'info': return '#3b82f6'
      case 'debug': return '#6b7280'
      default: return '#6b7280'
    }
  }

  const getLogLevelStyles = (level: string) => {
    switch (level) {
      case 'error':
        return { icon: '❌', badge: 'error' }
      case 'warn':
        return { icon: '⚠️', badge: 'warning' }
      case 'info':
        return { icon: 'ℹ️', badge: 'info' }
      case 'debug':
        return { icon: '🔍', badge: 'secondary' }
      default:
        return { icon: '📝', badge: 'secondary' }
    }
  }





  if (!isOpen) return null

  return (
    <Portal>
      <div className="modal-overlay" onClick={onClose}>
        <div
          className="modal-content"
          style={{
            width: '1000px',
            maxWidth: '90vw',
            height: '90vh',
            maxHeight: '90vh',
            margin: '5vh auto',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            backgroundColor: '#fbf9f8'
          }}
          onClick={(e) => e.stopPropagation()}
        >
        {/* Header */}
        <div className="dashboard-header" style={{
          flexShrink: 0,
          borderBottom: '1px solid #e5e7eb',
          margin: 0,
          backgroundColor: '#fbf9f8'
        }}>
          <div className="dashboard-header-content">
            <p className="dashboard-title" style={{ fontSize: '1.5rem' }}>
              🚀 Flödesloggar
            </p>
            <p className="dashboard-subtitle">
              {execution ? `Körning ${execution.id.slice(0, 8)}...` : 'Ingen aktiv körning'}
            </p>
          </div>
          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
            {execution && (execution.status === 'running' || execution.status === 'pending') && (
              <button
                onClick={handleCancel}
                disabled={cancelling}
                className="action-button secondary"
                style={{
                  backgroundColor: cancelling ? '#f3f4f6' : '#ffffff',
                  color: cancelling ? '#6b7280' : '#ef4444',
                  border: '1px solid #ef4444',
                  opacity: cancelling ? 0.5 : 1,
                  cursor: cancelling ? 'not-allowed' : 'pointer'
                }}
              >
                <span>{cancelling ? '⏳ Avbryter...' : '⏹️ Avbryt'}</span>
              </button>
            )}
            <button
              onClick={onClose}
              className="action-button secondary"
            >
              <span>✕ Stäng</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div style={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#ffffff'
        }}>
          {!execution ? (
            <div className="empty-state">
              <div className="empty-state-icon">📋</div>
              <h3 className="empty-state-title">Ingen körning aktiv</h3>
              <p className="empty-state-subtitle">Starta ett flöde för att se loggar här</p>
            </div>
          ) : (
            <div style={{ flex: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
              {/* Logs Table */}
              {loading && logs.length === 0 ? (
                <div className="empty-state">
                  <div className="empty-state-icon">⏳</div>
                  <h3 className="empty-state-title">Laddar loggar...</h3>
                  <p className="empty-state-subtitle">Väntar på loggdata</p>
                </div>
              ) : logs.length === 0 ? (
                <div className="empty-state">
                  <div className="empty-state-icon">📝</div>
                  <h3 className="empty-state-title">Inga loggar än</h3>
                  <p className="empty-state-subtitle">Loggar kommer att visas här när flödet körs</p>
                </div>
              ) : (
                <div className="table-container custom-scrollbar" style={{
                  flex: 1,
                  overflow: 'auto',
                  padding: '1.5rem'
                }}>
                  <table className="table" style={{ tableLayout: 'fixed', width: '100%' }}>
                    <thead>
                      <tr>
                        <th style={{ width: '100px' }}>Nivå</th>
                        <th style={{ width: '120px' }}>Tid</th>
                        <th style={{ width: '100px' }}>Steg</th>
                        <th style={{ width: 'auto' }}>Meddelande</th>
                        <th style={{ width: '100px' }}>Data</th>
                      </tr>
                    </thead>
                    <tbody>
                      {logs.map((log, index) => {
                        const styles = getLogLevelStyles(log.level);
                        return (
                          <tr key={index}>
                            <td>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                                <span>{styles.icon}</span>
                                <span className={`log-badge ${styles.badge}`}>
                                  {log.level.toUpperCase()}
                                </span>
                              </div>
                            </td>
                            <td className="secondary-text" style={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                              {new Date(log.timestamp).toLocaleTimeString('sv-SE', {
                                hour12: false,
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                              })}.{String(new Date(log.timestamp).getMilliseconds()).padStart(3, '0')}
                            </td>
                            <td>
                              {log.stepId ? (
                                <span className="step-badge">
                                  {log.stepId.slice(0, 8)}
                                </span>
                              ) : (
                                <span className="secondary-text">—</span>
                              )}
                            </td>
                            <td style={{ lineHeight: '1.5', wordWrap: 'break-word' }}>
                              {log.message}
                            </td>
                            <td>
                              {log.data ? (
                                <details className="data-popup-container">
                                  <summary className="data-link">
                                    Visa data
                                  </summary>
                                  <div className="data-popup">
                                    <pre>
                                      {JSON.stringify(log.data, null, 2)}
                                    </pre>
                                  </div>
                                </details>
                              ) : (
                                <span className="secondary-text">—</span>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                  <div ref={logsEndRef} />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
    </Portal>
  )
}
