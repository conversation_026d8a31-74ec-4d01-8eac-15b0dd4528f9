{"name": "Variable Interpolation Demo", "description": "Demonstrerar hur variabler kan extraheras och användas i efterföljande steg", "steps": [{"id": "step1", "type": "navigate", "url": "https://example.com", "waitUntil": "load", "timeout": 30000, "description": "Navigera till example.com"}, {"id": "step2", "type": "extractText", "selector": "h1", "variableName": "pageTitle", "timeout": 5000, "description": "<PERSON>hera sidans titel"}, {"id": "step3", "type": "extractText", "selector": "p", "variableName": "pageDescription", "timeout": 5000, "description": "<PERSON><PERSON><PERSON> sidans beskriv<PERSON>"}, {"id": "step4", "type": "navigate", "url": "https://httpbin.org/forms/post", "waitUntil": "load", "timeout": 30000, "description": "Navigera till testformulär"}, {"id": "step5", "type": "fill", "selector": "input[name='custname']", "value": "Titel: ${pageTitle}", "timeout": 5000, "description": "Fyll i kundnamn med extraherad titel"}, {"id": "step6", "type": "fill", "selector": "textarea[name='comments']", "value": "Beskrivning från föregående sida: ${pageDescription}", "timeout": 5000, "description": "Fyll i kommentarer med extraherad beskrivning"}, {"id": "step7", "type": "extractAttribute", "selector": "form", "attribute": "action", "variableName": "formAction", "timeout": 5000, "description": "Extrahera formulärets action-attribut"}, {"id": "step8", "type": "takeScreenshot", "path": "form-filled-with-${pageTitle}.png", "fullPage": true, "timeout": 5000, "description": "Ta skärmbild med dynamiskt filnamn"}], "settings": {"browser": "chromium", "headless": false, "viewport": {"width": 1280, "height": 720}}}