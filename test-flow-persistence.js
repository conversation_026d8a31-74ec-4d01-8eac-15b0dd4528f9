const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testFlowPersistence() {
  console.log('🧪 Testing Flow Persistence Fix...\n');

  try {
    // Test 1: Check if API is running
    console.log('1. Checking API health...');
    await axios.get('http://localhost:3001/health');
    console.log('✅ API is healthy\n');

    // Test 2: List existing flows
    console.log('2. Listing existing flows...');
    const listResponse = await axios.get(`${API_BASE}/flows`);
    console.log(`📋 Found ${listResponse.data.data.length} existing flows`);
    if (listResponse.data.data.length > 0) {
      listResponse.data.data.forEach(flow => {
        console.log(`   - ${flow.name} (ID: ${flow.id})`);
      });
    }
    console.log();

    // Test 3: Create a simple test flow
    console.log('3. Creating a simple test flow...');
    const testFlow = {
      name: 'Test Flow - Persistence Check',
      description: 'A simple flow to test persistence',
      steps: [
        {
          id: 'step1',
          type: 'navigate',
          url: 'https://example.com',
          waitUntil: 'load',
          timeout: 30000,
          description: 'Navigate to example.com'
        }
      ],
      settings: {
        browserType: 'chromium',
        headless: true,
        viewportWidth: 1280,
        viewportHeight: 720
      }
    };

    const createResponse = await axios.post(`${API_BASE}/flows`, testFlow);
    const flowId = createResponse.data.data.id;
    console.log(`✅ Flow created with ID: ${flowId}\n`);

    // Test 4: Immediately try to retrieve the flow
    console.log('4. Retrieving the flow immediately...');
    const getResponse = await axios.get(`${API_BASE}/flows/${flowId}`);
    console.log(`✅ Flow retrieved: ${getResponse.data.data.name}\n`);

    // Test 5: Try to execute the flow
    console.log('5. Attempting to execute the flow...');
    const executeResponse = await axios.post(`${API_BASE}/executions`, {
      flowId: flowId,
      variables: {}
    });
    const executionId = executeResponse.data.data.id;
    console.log(`✅ Flow execution started with ID: ${executionId}\n`);

    // Test 6: Wait a bit and check execution status
    console.log('6. Waiting 3 seconds and checking execution status...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const executionResponse = await axios.get(`${API_BASE}/executions/${executionId}`);
    console.log(`📊 Execution status: ${executionResponse.data.data.status}`);
    console.log(`📋 Execution logs: ${executionResponse.data.data.logs.length} entries`);
    
    if (executionResponse.data.data.logs.length > 0) {
      console.log('Recent logs:');
      executionResponse.data.data.logs.slice(-3).forEach(log => {
        console.log(`   [${log.level}] ${log.message}`);
      });
    }

    console.log('\n🎉 Test completed successfully!');
    console.log('The flow persistence issue should now be fixed.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data?.error) {
      console.error('Error details:', error.response.data.error);
    }
  }
}

// Run the test
testFlowPersistence();
