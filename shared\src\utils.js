// Additional utility functions for working with RPA flows
// Note: Core functions like generateId, createEmpty<PERSON>low, etc. are exported from types.ts
export function formatDuration(ms) {
    if (ms < 1000) {
        return `${ms}ms`;
    }
    const seconds = Math.floor(ms / 1000);
    if (seconds < 60) {
        return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
}
/**
 * Variable interpolation utilities for RPA flows
 */
/**
 * Interpolates variables in a string using ${variableName} syntax
 * @param text The text containing variable references
 * @param variables The variables object containing values
 * @returns The text with variables replaced by their values
 */
export function interpolateVariables(text, variables) {
    if (!text || typeof text !== 'string') {
        return text;
    }
    // Replace ${variableName} with actual values
    return text.replace(/\$\{([^}]+)\}/g, (match, variableName) => {
        const trimmedName = variableName.trim();
        if (trimmedName in variables) {
            const value = variables[trimmedName];
            // Convert to string, handling null/undefined
            return value != null ? String(value) : '';
        }
        // If variable not found, leave the placeholder as is
        return match;
    });
}
/**
 * Checks if a string contains variable references
 * @param text The text to check
 * @returns True if the text contains ${variableName} patterns
 */
export function hasVariableReferences(text) {
    if (!text || typeof text !== 'string') {
        return false;
    }
    return /\$\{[^}]+\}/.test(text);
}
/**
 * Extracts all variable names referenced in a string
 * @param text The text to analyze
 * @returns Array of variable names found in the text
 */
export function extractVariableNames(text) {
    if (!text || typeof text !== 'string') {
        return [];
    }
    const matches = text.match(/\$\{([^}]+)\}/g);
    if (!matches) {
        return [];
    }
    return matches.map(match => {
        const variableName = match.slice(2, -1).trim(); // Remove ${ and }
        return variableName;
    });
}
/**
 * Validates that all variable references in a text can be resolved
 * @param text The text containing variable references
 * @param variables The available variables
 * @returns Object with validation result and missing variables
 */
export function validateVariableReferences(text, variables) {
    const referencedVariables = extractVariableNames(text);
    const missingVariables = referencedVariables.filter(varName => !(varName in variables));
    return {
        valid: missingVariables.length === 0,
        missingVariables
    };
}
//# sourceMappingURL=utils.js.map