import { useState, useEffect, useCallback, useRef } from 'react'
import { Link } from 'react-router-dom'
import { Rpa<PERSON>low, Customer } from '@rpa-project/shared'
import { flowApi, customerApi } from '../services/api'

export function FlowList() {
  const [flows, setFlows] = useState<RpaFlow[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)
  const [searchQuery, setSearchQuery] = useState('')
  const observerRef = useRef<IntersectionObserver | null>(null)
  const lastFlowElementRef = useRef<HTMLTableRowElement | null>(null)

  const ITEMS_PER_PAGE = 20

  useEffect(() => {
    loadFlows(true)
    loadCustomers()
  }, [])

  const loadCustomers = async () => {
    try {
      const response = await customerApi.getCustomers()
      if (response.success && response.data) {
        setCustomers(response.data)
      }
    } catch (err) {
      console.error('Error loading customers:', err)
    }
  }

  const getCustomerInfo = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    return customer ? `${customer.customerNumber} - ${customer.name}` : 'Okänd kund'
  }

  // Filter flows based on search query
  const filteredFlows = flows.filter(flow => {
    if (!searchQuery.trim()) return true

    const query = searchQuery.toLowerCase()
    const customer = customers.find(c => c.id === flow.customerId)

    // Search in flow name
    if (flow.name.toLowerCase().includes(query)) return true

    // Search in flow description
    if (flow.description?.toLowerCase().includes(query)) return true

    // Search in customer number and name
    if (customer) {
      if (customer.customerNumber.toLowerCase().includes(query)) return true
      if (customer.name.toLowerCase().includes(query)) return true
    }

    return false
  })

  // Intersection Observer for infinite scroll
  const lastFlowElementRefCallback = useCallback((node: HTMLTableRowElement | null) => {
    if (loading || loadingMore) return
    if (observerRef.current) observerRef.current.disconnect()

    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadFlows(false)
      }
    })

    if (node) observerRef.current.observe(node)
  }, [loading, loadingMore, hasMore])

  // Cleanup observer on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  const loadFlows = async (reset = false) => {
    try {
      if (reset) {
        setLoading(true)
        setOffset(0)
      } else {
        setLoadingMore(true)
      }

      const currentOffset = reset ? 0 : offset
      const response = await flowApi.getFlows({
        limit: ITEMS_PER_PAGE,
        offset: currentOffset
      })

      const newFlows = response.data || []

      if (reset) {
        setFlows(newFlows)
      } else {
        setFlows(prev => [...prev, ...newFlows])
      }

      // Check if we have more data
      setHasMore(newFlows.length === ITEMS_PER_PAGE)
      setOffset(currentOffset + newFlows.length)
      setError(null)
    } catch (err) {
      setError('Failed to load flows')
      console.error('Error loading flows:', err)
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  const handleDeleteFlow = async (id: string) => {
    if (!confirm('Are you sure you want to delete this flow?')) {
      return
    }

    try {
      await flowApi.deleteFlow(id)
      setFlows(flows.filter(flow => flow.id !== id))
      // Adjust offset since we removed an item
      setOffset(prev => Math.max(0, prev - 1))
    } catch (err) {
      setError('Failed to delete flow')
      console.error('Error deleting flow:', err)
    }
  }

  const handleExecuteFlow = async (id: string) => {
    try {
      await flowApi.executeFlow(id)
      alert('Flow execution started! Check the Executions page for progress.')
    } catch (err) {
      setError('Failed to execute flow')
      console.error('Error executing flow:', err)
    }
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar flöden...</div>
      </div>
    )
  }

  const getFlowStats = () => {
    const total = flows.length
    const totalSteps = flows.reduce((sum, flow) => sum + flow.steps.length, 0)
    const avgSteps = total > 0 ? Math.round(totalSteps / total) : 0
    const recentlyUpdated = flows.filter(flow => {
      const daysSinceUpdate = (Date.now() - new Date(flow.updatedAt).getTime()) / (1000 * 60 * 60 * 24)
      return daysSinceUpdate <= 7
    }).length

    return { total, totalSteps, avgSteps, recentlyUpdated }
  }

  // const stats = getFlowStats() // Removed for now, can be used later for statistics

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Flöden</p>
          <p className="dashboard-subtitle">
            Hantera dina automatiseringsflöden och arbetsflöden.
          </p>
        </div>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <div style={{ position: 'relative' }}>
            <input
              type="text"
              placeholder="🔎 Sök flöden, kunder..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                style={{
                  position: 'absolute',
                  right: '0.5rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  color: '#6b7280',
                  fontSize: '1rem'
                }}
              >
                ✕
              </button>
            )}
          </div>
          <Link to="/flows/new" className="action-button primary">
            <span>Skapa nytt flöde</span>
          </Link>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-card">
          <h3 className="error-title">Fel vid laddning av skript</h3>
          <p className="error-message">{error}</p>
        </div>
      )}

      {/* Flows List */}
      {flows.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">🤖</div>
            <p className="empty-state-title">Inga flöden än</p>
            <p className="empty-state-subtitle">Skapa ditt första flöde för att komma igång</p>
          </div>
        </div>
      ) : filteredFlows.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">🔍</div>
            <p className="empty-state-title">Inga flöden hittades</p>
            <p className="empty-state-subtitle">Försök med en annan sökning eller skapa ett nytt flöde</p>
            <button
              onClick={() => setSearchQuery('')}
              className="action-button secondary centered"
              style={{ marginTop: '1rem' }}
            >
              <span>Rensa sökning</span>
            </button>
          </div>
        </div>
      ) : (
        <>
          <h2 className="section-title">
            {searchQuery ? `Sökresultat (${filteredFlows.length})` : 'Dina flöden'}
          </h2>
          <div className="table-container">
            <div className="activity-table">
              <table className="table">
                <thead>
                  <tr>
                    <th>Flödesnamn</th>
                    <th>Kund</th>
                    <th>Antal steg</th>
                    <th>Senaste uppdatering</th>
                    <th>Åtgärder</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredFlows.map((flow, index) => (
                    <tr
                      key={flow.id}
                      ref={index === filteredFlows.length - 1 ? lastFlowElementRefCallback : null}
                    >
                      <td>
                        <Link to={`/flows/${flow.id}`} className="flow-link">
                          <div className="flow-name">{flow.name}</div>
                          {flow.description && (
                            <div className="flow-description-small">{flow.description}</div>
                          )}
                        </Link>
                      </td>
                      <td className="secondary-text">
                        {getCustomerInfo(flow.customerId)}
                      </td>
                      <td>{flow.steps.length}</td>
                      <td className="secondary-text">
                        {new Date(flow.updatedAt).toLocaleDateString('sv-SE')}
                      </td>
                      <td>
                        <div className="flow-actions">
                          <button
                            onClick={() => handleExecuteFlow(flow.id)}
                            className="action-button-small icon-only"
                            title="Kör skript"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <polygon points="5,3 19,12 5,21"></polygon>
                            </svg>
                          </button>
                          <Link
                            to={`/flows/${flow.id}`}
                            className="action-button-small icon-only"
                            title="Redigera skript"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </Link>
                          <button
                            onClick={() => handleDeleteFlow(flow.id)}
                            className="action-button-small danger"
                            title="Ta bort skript"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <polyline points="3,6 5,6 21,6"></polyline>
                              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                              <line x1="10" y1="11" x2="10" y2="17"></line>
                              <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Loading more indicator */}
            {loadingMore && (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                padding: '1rem',
                color: '#6b7280'
              }}>
                <div>Laddar fler flöden...</div>
              </div>
            )}

            {/* End of list indicator */}
            {!hasMore && flows.length > 0 && (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                padding: '1rem',
                color: '#6b7280',
                fontSize: '0.875rem'
              }}>
                <div>Alla flöden har laddats</div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}
