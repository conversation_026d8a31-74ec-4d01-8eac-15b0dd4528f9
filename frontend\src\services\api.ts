import axios from 'axios'
import {
  RpaFlow,
  FlowExecution,
  CreateFlowRequest,
  UpdateFlowRequest,
  ExecuteFlowRequest,
  FlowSchedule,
  CreateScheduleRequest,
  UpdateScheduleRequest,
  ScheduleQuery,
  CronExpressionInfo,
  ApiResponse,
  Credential,
  CreatePasswordCredentialRequest,
  CreateTwoFactorCredentialRequest,
  UpdatePasswordCredentialRequest,
  UpdateTwoFactorCredentialRequest,
  Customer,
  CustomerToken,
  CreateCustomerRequest,
  UpdateCustomerRequest,
  CreateCustomerTokenRequest,
  UpdateCustomerTokenRequest
} from '@rpa-project/shared'
import { aiService } from './ai'

const API_BASE_URL = '/api'

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for logging
api.interceptors.request.use((config) => {
  console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
  return config
})

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// Flow API
export const flowApi = {
  async getFlows(params?: {
    limit?: number
    offset?: number
  }): Promise<ApiResponse<RpaFlow[]>> {
    const response = await api.get('/flows', { params })
    return response.data
  },

  async getFlow(id: string): Promise<ApiResponse<RpaFlow>> {
    const response = await api.get(`/flows/${id}`)
    return response.data
  },

  async createFlow(flow: CreateFlowRequest): Promise<ApiResponse<RpaFlow>> {
    const response = await api.post('/flows', flow)
    return response.data
  },

  async updateFlow(id: string, flow: UpdateFlowRequest): Promise<ApiResponse<RpaFlow>> {
    const response = await api.put(`/flows/${id}`, flow)
    return response.data
  },

  async deleteFlow(id: string): Promise<ApiResponse> {
    const response = await api.delete(`/flows/${id}`)
    return response.data
  },

  async validateFlow(id: string): Promise<ApiResponse> {
    const response = await api.post(`/flows/${id}/validate`)
    return response.data
  },

  async executeFlow(flowId: string, variables?: Record<string, any>): Promise<ApiResponse<FlowExecution>> {
    const request: ExecuteFlowRequest = { flowId, variables }
    const response = await api.post('/executions', request)
    return response.data
  }
}

// Execution API
export const executionApi = {
  async getExecutions(params?: {
    flowId?: string
    status?: string
    limit?: number
    offset?: number
  }): Promise<ApiResponse<FlowExecution[]>> {
    const response = await api.get('/executions', { params })
    return response.data
  },

  async getExecution(id: string): Promise<ApiResponse<FlowExecution>> {
    const response = await api.get(`/executions/${id}`)
    return response.data
  },

  async cancelExecution(id: string): Promise<ApiResponse> {
    const response = await api.post(`/executions/${id}/cancel`)
    return response.data
  },

  async getExecutionLogs(id: string): Promise<ApiResponse> {
    const response = await api.get(`/executions/${id}/logs`)
    return response.data
  }
}

// Queue API
export const queueApi = {
  async getQueueStats(): Promise<ApiResponse> {
    const response = await api.get('/queue/stats')
    return response.data
  },

  async getQueueJobs(params?: {
    status?: string
    limit?: number
    offset?: number
  }): Promise<ApiResponse> {
    const response = await api.get('/queue/jobs', { params })
    return response.data
  },

  async pauseQueue(): Promise<ApiResponse> {
    const response = await api.post('/queue/pause')
    return response.data
  },

  async resumeQueue(): Promise<ApiResponse> {
    const response = await api.post('/queue/resume')
    return response.data
  },

  async cleanQueue(olderThan?: number): Promise<ApiResponse> {
    const response = await api.delete('/queue/clean', {
      params: { olderThan }
    })
    return response.data
  }
}

// Schedule API
export const scheduleApi = {
  async getSchedules(params?: ScheduleQuery): Promise<ApiResponse<FlowSchedule[]>> {
    const response = await api.get('/schedules', { params })
    return response.data
  },

  async getSchedule(id: string): Promise<ApiResponse<FlowSchedule>> {
    const response = await api.get(`/schedules/${id}`)
    return response.data
  },

  async createSchedule(schedule: CreateScheduleRequest): Promise<ApiResponse<FlowSchedule>> {
    const response = await api.post('/schedules', schedule)
    return response.data
  },

  async updateSchedule(id: string, schedule: UpdateScheduleRequest): Promise<ApiResponse<FlowSchedule>> {
    const response = await api.put(`/schedules/${id}`, schedule)
    return response.data
  },

  async deleteSchedule(id: string): Promise<ApiResponse> {
    const response = await api.delete(`/schedules/${id}`)
    return response.data
  },

  async toggleSchedule(id: string): Promise<ApiResponse<FlowSchedule>> {
    const response = await api.post(`/schedules/${id}/toggle`)
    return response.data
  },

  async validateCronExpression(cronExpression: string, timezone?: string): Promise<ApiResponse<CronExpressionInfo>> {
    const response = await api.post('/schedules/validate-cron', { cronExpression, timezone })
    return response.data
  },

  async getSchedulesByFlowId(flowId: string): Promise<ApiResponse<FlowSchedule[]>> {
    const response = await api.get(`/schedules/flow/${flowId}`)
    return response.data
  },

  async getScheduleStats(): Promise<ApiResponse> {
    const response = await api.get('/schedules/stats')
    return response.data
  },

  async getSchedulerStats(): Promise<ApiResponse> {
    const response = await api.get('/schedules/stats')
    return response.data
  }
}

// Credential API
export const credentialApi = {
  async getCredentials(type?: 'password' | '2fa'): Promise<ApiResponse<Credential[]>> {
    const params = type ? { type } : {}
    const response = await api.get('/credentials', { params })
    return response.data
  },

  async getCredential(id: string): Promise<ApiResponse<Credential>> {
    const response = await api.get(`/credentials/${id}`)
    return response.data
  },

  async createPasswordCredential(credential: CreatePasswordCredentialRequest): Promise<ApiResponse<Credential>> {
    const response = await api.post('/credentials/password', credential)
    return response.data
  },

  async createTwoFactorCredential(credential: CreateTwoFactorCredentialRequest): Promise<ApiResponse<Credential>> {
    const response = await api.post('/credentials/2fa', credential)
    return response.data
  },

  async updatePasswordCredential(id: string, credential: UpdatePasswordCredentialRequest): Promise<ApiResponse<Credential>> {
    const response = await api.put(`/credentials/${id}/password`, credential)
    return response.data
  },

  async updateTwoFactorCredential(id: string, credential: UpdateTwoFactorCredentialRequest): Promise<ApiResponse<Credential>> {
    const response = await api.put(`/credentials/${id}/2fa`, credential)
    return response.data
  },

  async deleteCredential(id: string): Promise<ApiResponse> {
    const response = await api.delete(`/credentials/${id}`)
    return response.data
  },

  async testTOTP(id: string): Promise<ApiResponse<{ code: string }>> {
    const response = await api.post(`/credentials/${id}/test-totp`)
    return response.data
  }
}

// Customer API
export const customerApi = {
  async getCustomers(): Promise<ApiResponse<Customer[]>> {
    const response = await api.get('/customers')
    return response.data
  },

  async getCustomer(id: string): Promise<ApiResponse<Customer>> {
    const response = await api.get(`/customers/${id}`)
    return response.data
  },

  async getCustomerByNumber(customerNumber: string): Promise<ApiResponse<Customer>> {
    const response = await api.get(`/customers/number/${customerNumber}`)
    return response.data
  },

  async createCustomer(customer: CreateCustomerRequest): Promise<ApiResponse<Customer>> {
    const response = await api.post('/customers', customer)
    return response.data
  },

  async updateCustomer(id: string, customer: UpdateCustomerRequest): Promise<ApiResponse<Customer>> {
    const response = await api.put(`/customers/${id}`, customer)
    return response.data
  },

  async deleteCustomer(id: string): Promise<ApiResponse> {
    const response = await api.delete(`/customers/${id}`)
    return response.data
  },



  // New customer token management methods
  async getCustomerTokenList(customerId: string): Promise<ApiResponse<CustomerToken[]>> {
    const response = await api.get(`/customers/${customerId}/tokens`)
    return response.data
  },

  async createCustomerToken(customerId: string, token: CreateCustomerTokenRequest): Promise<ApiResponse<CustomerToken>> {
    const response = await api.post(`/customers/${customerId}/tokens`, token)
    return response.data
  },

  async getCustomerToken(customerId: string, tokenId: string): Promise<ApiResponse<CustomerToken>> {
    const response = await api.get(`/customers/${customerId}/tokens/${tokenId}`)
    return response.data
  },

  async updateCustomerToken(customerId: string, tokenId: string, token: UpdateCustomerTokenRequest): Promise<ApiResponse<CustomerToken>> {
    const response = await api.put(`/customers/${customerId}/tokens/${tokenId}`, token)
    return response.data
  },

  async deleteCustomerToken(customerId: string, tokenId: string): Promise<ApiResponse> {
    const response = await api.delete(`/customers/${customerId}/tokens/${tokenId}`)
    return response.data
  },

  async getCustomerTokenData(customerId: string, tokenId: string): Promise<ApiResponse<{ apiToken?: string; refreshToken?: string }>> {
    const response = await api.get(`/customers/${customerId}/tokens/${tokenId}/data`)
    return response.data
  }
}

// Export AI service as part of the API
export { aiService }

export default api
