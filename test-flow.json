{"name": "Example Web Automation Flow", "description": "A sample flow that demonstrates basic web automation capabilities", "steps": [{"id": "step1", "type": "navigate", "url": "https://example.com", "waitUntil": "load", "timeout": 30000, "description": "Navigate to example.com"}, {"id": "step2", "type": "waitForSelector", "selector": "h1", "state": "visible", "timeout": 10000, "description": "Wait for the main heading to be visible"}, {"id": "step3", "type": "extractText", "selector": "h1", "variableName": "pageTitle", "timeout": 5000, "description": "Extract the page title"}, {"id": "step4", "type": "takeScreenshot", "path": "example-page.png", "fullPage": true, "timeout": 5000, "description": "Take a screenshot of the page"}], "settings": {"browserType": "chromium", "headless": true, "viewportWidth": 1280, "viewportHeight": 720, "defaultTimeout": 30000}}