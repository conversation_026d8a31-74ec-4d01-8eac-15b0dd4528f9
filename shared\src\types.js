// Base types for RPA automation
// Utility function for generating IDs
export function generateId() {
    return Math.random().toString(36).substring(2, 11) + Date.now().toString(36);
}
// Utility functions for schedules
export function createEmptySchedule(flowId, name) {
    return {
        id: generateId(),
        flowId,
        name,
        description: '',
        cronExpression: '0 9 * * 1-5', // Default: 9 AM on weekdays
        timezone: 'UTC',
        enabled: true,
        variables: {},
        createdAt: new Date(),
        updatedAt: new Date()
    };
}
// Common cron expressions
export const COMMON_CRON_EXPRESSIONS = {
    'Every minute': '* * * * *',
    'Every 5 minutes': '*/5 * * * *',
    'Every 15 minutes': '*/15 * * * *',
    'Every 30 minutes': '*/30 * * * *',
    'Every hour': '0 * * * *',
    'Every 2 hours': '0 */2 * * *',
    'Every 6 hours': '0 */6 * * *',
    'Every 12 hours': '0 */12 * * *',
    'Daily at 9 AM': '0 9 * * *',
    'Daily at 6 PM': '0 18 * * *',
    'Weekdays at 9 AM': '0 9 * * 1-5',
    'Weekends at 10 AM': '0 10 * * 6,0',
    'Weekly on Monday at 9 AM': '0 9 * * 1',
    'Monthly on 1st at 9 AM': '0 9 1 * *',
    'Yearly on Jan 1st at 9 AM': '0 9 1 1 *'
};
export function getCronDescription(cronExpression) {
    // Find matching common expression
    for (const [description, expression] of Object.entries(COMMON_CRON_EXPRESSIONS)) {
        if (expression === cronExpression) {
            return description;
        }
    }
    // Return the expression itself if no match found
    return cronExpression;
}
// Utility functions for creating steps
export function createEmptyFlow(name, customerId = '') {
    return {
        id: generateId(),
        name,
        description: '',
        customerId,
        steps: [],
        createdAt: new Date(),
        updatedAt: new Date()
    };
}
export function createStepFromType(stepType) {
    const baseStep = {
        id: generateId(),
        type: stepType,
        timeout: 30000
    };
    switch (stepType) {
        case 'navigate':
            return { ...baseStep, type: 'navigate', url: 'https://example.com', waitUntil: 'load' };
        case 'goBack':
            return { ...baseStep, type: 'goBack' };
        case 'goForward':
            return { ...baseStep, type: 'goForward' };
        case 'reload':
            return { ...baseStep, type: 'reload' };
        case 'click':
            return { ...baseStep, type: 'click', selector: 'button', button: 'left' };
        case 'fill':
            return { ...baseStep, type: 'fill', selector: 'input', value: 'placeholder text' };
        case 'type':
            return { ...baseStep, type: 'type', selector: 'input', text: 'text to type', delay: 0 };
        case 'selectOption':
            return { ...baseStep, type: 'selectOption', selector: 'select', value: 'option-value' };
        case 'check':
            return { ...baseStep, type: 'check', selector: 'input[type="checkbox"]' };
        case 'uncheck':
            return { ...baseStep, type: 'uncheck', selector: 'input[type="checkbox"]' };
        case 'waitForSelector':
            return { ...baseStep, type: 'waitForSelector', selector: 'element', state: 'visible' };
        case 'waitForTimeout':
            return { ...baseStep, type: 'waitForTimeout', duration: 1000 };
        case 'extractText':
            return { ...baseStep, type: 'extractText', selector: 'element', variableName: 'extractedText' };
        case 'takeScreenshot':
            return { ...baseStep, type: 'takeScreenshot', fullPage: false };
        case 'conditionalClick':
            return { ...baseStep, type: 'conditionalClick', selector: 'button', condition: 'exists', clickIfTrue: true, button: 'left' };
        case 'fillPassword':
            return { ...baseStep, type: 'fillPassword', selector: 'input[type="password"]', credentialId: '' };
        case 'fill2FA':
            return { ...baseStep, type: 'fill2FA', selector: 'input', credentialId: '' };
        case 'downloadFile':
            return { ...baseStep, type: 'downloadFile', triggerSelector: 'a[href*=".pdf"]', filename: '', variableName: '', saveToFile: false, forceDownload: false };
        default:
            throw new Error(`Unknown step type: ${stepType}`);
    }
}
export function getStepLabel(step) {
    switch (step.type) {
        case 'navigate':
            return step.url || 'Navigate to URL';
        case 'goBack':
            return 'Go back';
        case 'goForward':
            return 'Go forward';
        case 'reload':
            return 'Reload page';
        case 'click':
            return step.selector || 'Click element';
        case 'fill':
            return `Fill: ${step.selector || 'element'}`;
        case 'type':
            return `Type: ${step.text || 'text'}`;
        case 'selectOption':
            return `Select: ${step.value || step.label || (step.index !== undefined ? `index ${step.index}` : 'option')}`;
        case 'check':
            return `Check: ${step.selector || 'checkbox'}`;
        case 'uncheck':
            return `Uncheck: ${step.selector || 'checkbox'}`;
        case 'waitForSelector':
            return `Wait for: ${step.selector || 'element'}`;
        case 'waitForTimeout':
            return `Wait ${step.duration || 1000}ms`;
        case 'extractText':
            return `Extract: ${step.variableName || 'text'}`;
        case 'takeScreenshot':
            return 'Take screenshot';
        case 'conditionalClick':
            const condStep = step;
            const action = condStep.clickIfTrue ? 'Click if' : 'Click if not';
            return `${action} ${condStep.condition}: ${condStep.selector || 'element'}`;
        case 'fillPassword':
            return `Fill password: ${step.selector || 'field'}`;
        case 'fill2FA':
            return `Fill 2FA: ${step.selector || 'field'}`;
        case 'downloadFile':
            const downloadStep = step;
            return downloadStep.triggerSelector
                ? `Download via: ${downloadStep.triggerSelector}`
                : 'Download file';
        default:
            return step.type;
    }
}
// React Flow conversion functions
export function convertFlowToReactFlow(flow) {
    const nodes = flow.steps.map((step, index) => {
        // Create better positioning for AI-generated flows
        // Use a slight horizontal offset to prevent overlapping
        const baseX = 100;
        const baseY = 50;
        const verticalSpacing = 120;
        const horizontalOffset = (index % 2) * 30; // Small alternating offset
        return {
            id: step.id,
            type: 'rpaStep',
            position: {
                x: baseX + horizontalOffset,
                y: baseY + (index * verticalSpacing)
            },
            data: {
                step,
                label: getStepLabel(step)
            }
        };
    });
    const edges = [];
    for (let i = 0; i < flow.steps.length - 1; i++) {
        edges.push({
            id: `${flow.steps[i].id}-${flow.steps[i + 1].id}`,
            source: flow.steps[i].id,
            target: flow.steps[i + 1].id,
            type: 'smoothstep'
        });
    }
    return { nodes, edges };
}
export function convertReactFlowToFlow(flowData, flowId, flowName, customerId = '') {
    // Build a map of connections
    const connections = new Map();
    flowData.edges.forEach(edge => {
        connections.set(edge.source, edge.target);
    });
    // Find the starting node (no incoming edges)
    const hasIncoming = new Set(flowData.edges.map(e => e.target));
    const startNodes = flowData.nodes.filter(node => !hasIncoming.has(node.id));
    if (startNodes.length === 0 && flowData.nodes.length > 0) {
        // If no clear start, use first node
        const steps = flowData.nodes.map(node => node.data.step);
        return {
            id: flowId,
            name: flowName,
            description: '',
            customerId,
            steps,
            createdAt: new Date(),
            updatedAt: new Date()
        };
    }
    // Build ordered steps by following connections
    const steps = [];
    const visited = new Set();
    function addStepsFromNode(nodeId) {
        if (visited.has(nodeId))
            return;
        visited.add(nodeId);
        const node = flowData.nodes.find(n => n.id === nodeId);
        if (node) {
            steps.push(node.data.step);
            const nextNodeId = connections.get(nodeId);
            if (nextNodeId) {
                addStepsFromNode(nextNodeId);
            }
        }
    }
    if (startNodes.length > 0) {
        addStepsFromNode(startNodes[0].id);
    }
    return {
        id: flowId,
        name: flowName,
        description: '',
        customerId,
        steps,
        createdAt: new Date(),
        updatedAt: new Date()
    };
}
//# sourceMappingURL=types.js.map