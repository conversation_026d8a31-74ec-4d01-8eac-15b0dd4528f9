#!/usr/bin/env node

/**
 * Command-line tool to check active runners and system status
 */

import { getRunnerFactory } from './runner/RunnerFactory';
import { getRunnerRegistry } from './runner/RunnerRegistry';

function formatStats(stats: any): string {
  return `
📊 Runner System Status:
  • Registered Runners: ${stats.registeredRunners}
  • Active Instances: ${stats.activeInstances}
  • Supported Step Types: ${stats.supportedStepTypes}
`;
}

function checkRunners() {
  console.log('🔍 Checking Runner System Status...\n');

  try {
    // Get factory stats
    const factory = getRunnerFactory();
    const factoryStats = factory.getStats();
    
    console.log(formatStats(factoryStats));

    // Get registry stats
    const registry = getRunnerRegistry();
    const registryStats = registry.getStats();
    
    console.log('🏭 Registry Details:');
    console.log(`  • Runner Types: ${registryStats.runnerTypes.join(', ')}`);
    
    // Check if there are active instances
    if (registryStats.activeInstances > 0) {
      console.log('\n⚠️  WARNING: There are active runner instances!');
      console.log('   This might indicate that cleanup was not performed properly.');
      console.log('   Active instances should be 0 when no flows are running.');
    } else {
      console.log('\n✅ All good! No active runner instances detected.');
    }

    // Get supported step types
    const supportedSteps = factory.getSupportedStepTypes();
    console.log('\n🛠️  Supported Step Types:');
    supportedSteps.forEach((stepType, index) => {
      console.log(`   ${index + 1}. ${stepType}`);
    });

    console.log('\n📋 Usage:');
    console.log('  • Run this command after flow execution to verify cleanup');
    console.log('  • Active instances should always be 0 when idle');
    console.log('  • If you see active instances, there might be a cleanup issue');

  } catch (error) {
    console.error('❌ Error checking runner status:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  checkRunners();
}

export { checkRunners };
