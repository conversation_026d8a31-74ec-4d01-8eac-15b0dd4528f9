import { useEffect, useRef, useState } from 'react'

interface UseScrollFadeOptions {
  fadeColor?: 'default' | 'white' | 'light'
}

export function useScrollFade(options: UseScrollFadeOptions = {}) {
  const { fadeColor = 'default' } = options
  const containerRef = useRef<HTMLDivElement>(null)
  const overlayRef = useRef<HTMLDivElement>(null)
  const [hasOverflow, setHasOverflow] = useState(false)

  useEffect(() => {
    const container = containerRef.current
    const overlay = overlayRef.current
    if (!container || !overlay) return

    const checkOverflow = () => {
      const isOverflowing = container.scrollHeight > container.clientHeight
      const isScrolledToBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 5

      // Show fade if there's overflow and we're not at the bottom
      const shouldShow = isOverflowing && !isScrolledToBottom
      setHasOverflow(shouldShow)

      // Update overlay visibility
      overlay.style.opacity = shouldShow ? '1' : '0'
    }

    // Check initially
    checkOverflow()

    // Check on scroll
    container.addEventListener('scroll', checkOverflow)

    // Check on resize
    const resizeObserver = new ResizeObserver(checkOverflow)
    resizeObserver.observe(container)

    // Check when content changes
    const mutationObserver = new MutationObserver(checkOverflow)
    mutationObserver.observe(container, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    })

    return () => {
      container.removeEventListener('scroll', checkOverflow)
      resizeObserver.disconnect()
      mutationObserver.disconnect()
    }
  }, [])

  const getFadeBackground = () => {
    switch (fadeColor) {
      case 'white':
        return 'linear-gradient(transparent, #ffffff)'
      case 'light':
        return 'linear-gradient(transparent, #f8fafc)'
      default:
        return 'linear-gradient(transparent, #fbf9f8)'
    }
  }

  const fadeOverlayStyle: React.CSSProperties = {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '20px',
    background: getFadeBackground(),
    pointerEvents: 'none',
    opacity: 0,
    transition: 'opacity 0.3s ease',
    zIndex: 10
  }

  return {
    containerRef,
    overlayRef,
    hasOverflow,
    fadeOverlayStyle
  }
}
