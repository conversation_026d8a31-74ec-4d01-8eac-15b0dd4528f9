import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Modal } from '../ui/Modal'
import { Customer } from '@rpa-project/shared'
import { customerApi } from '../../services/api'

interface FlowInfoModalProps {
  isOpen: boolean
  onClose: () => void
  flow: {
    name: string
    description?: string
    customerId?: string
  }
  onFlowChange: (updates: { name?: string; description?: string; customerId?: string }) => void
  onSave?: () => Promise<void>
}

export function FlowInfoModal({ isOpen, onClose, flow, onFlowChange, onSave }: FlowInfoModalProps) {
  const navigate = useNavigate()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [saving, setSaving] = useState(false)
  const [customerSearch, setCustomerSearch] = useState('')
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false)

  const canClose = flow.name.trim() !== '' && flow.description?.trim() !== '' && flow.customerId?.trim() !== ''

  // Filter customers based on search
  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
    customer.customerNumber.toLowerCase().includes(customerSearch.toLowerCase())
  )

  // Get selected customer for display
  const selectedCustomer = customers.find(c => c.id === flow.customerId)

  useEffect(() => {
    if (isOpen) {
      loadCustomers()
    }
  }, [isOpen])

  const loadCustomers = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await customerApi.getCustomers()
      if (response.success && response.data) {
        setCustomers(response.data)
      } else {
        setError(response.error || 'Misslyckades att ladda kunder')
      }
    } catch (err) {
      setError('Misslyckades att ladda kunder')
      console.error('Error loading customers:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCustomerSelect = (customer: Customer) => {
    onFlowChange({ customerId: customer.id })
    setCustomerSearch('')
    setShowCustomerDropdown(false)
  }

  const handleCancel = () => {
    navigate('/flows')
  }

  const handleClose = async () => {
    if (canClose && onSave) {
      try {
        setSaving(true)
        setError(null)
        await onSave()
        onClose()
      } catch (err) {
        setError('Misslyckades att spara flödet')
        console.error('Error saving flow:', err)
      } finally {
        setSaving(false)
      }
    } else if (canClose) {
      onClose()
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {}} // Disable closing via overlay/escape
      title="Skapa nytt flöde"
      size="md"
      showCloseButton={false} // Remove close button
    >
      <div style={{ padding: '1.5rem' }}>
        {error && (
          <div className="error-card" style={{ marginBottom: '1rem' }}>
            <h3 className="error-title">Fel</h3>
            <p className="error-message">{error}</p>
          </div>
        )}

        {/* Flow Information Fields */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr',
          gap: '1rem'
        }}>
          <div className="form-group">
            <label className="form-label">Kund *</label>
            {loading ? (
              <div style={{ padding: '0.75rem', color: '#6b7280' }}>Laddar kunder...</div>
            ) : (
              <div style={{ position: 'relative' }}>
                <input
                  type="text"
                  className="form-input"
                  value={selectedCustomer ? `${selectedCustomer.name} (${selectedCustomer.customerNumber})` : customerSearch}
                  onChange={(e) => {
                    setCustomerSearch(e.target.value)
                    setShowCustomerDropdown(true)
                    if (!selectedCustomer) {
                      // Clear selection if typing and no customer selected
                      onFlowChange({ customerId: '' })
                    }
                  }}
                  onFocus={() => {
                    setShowCustomerDropdown(true)
                    if (selectedCustomer) {
                      setCustomerSearch('')
                    }
                  }}
                  onBlur={() => {
                    // Delay hiding dropdown to allow for clicks
                    setTimeout(() => setShowCustomerDropdown(false), 200)
                  }}
                  placeholder="Sök efter kund..."
                  style={{
                    fontSize: '1rem',
                    borderColor: (flow.customerId?.trim() || '') === '' ? '#ef4444' : '#d1d5db'
                  }}
                  required
                />

                {showCustomerDropdown && filteredCustomers.length > 0 && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    right: 0,
                    backgroundColor: '#ffffff',
                    border: '1px solid #d1d5db',
                    borderTop: 'none',
                    borderRadius: '0 0 0.375rem 0.375rem',
                    maxHeight: '200px',
                    overflowY: 'auto',
                    zIndex: 1000,
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}>
                    {filteredCustomers.map((customer) => (
                      <div
                        key={customer.id}
                        onClick={() => handleCustomerSelect(customer)}
                        style={{
                          padding: '0.75rem',
                          cursor: 'pointer',
                          borderBottom: '1px solid #f3f4f6',
                          backgroundColor: customer.id === flow.customerId ? '#f9fafb' : 'transparent'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#f9fafb'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = customer.id === flow.customerId ? '#f9fafb' : 'transparent'
                        }}
                      >
                        <div style={{ fontWeight: '500' }}>{customer.name}</div>
                        <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>{customer.customerNumber}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
            {(flow.customerId?.trim() || '') === '' && (
              <p style={{ fontSize: '0.75rem', color: '#ef4444', marginTop: '0.25rem' }}>
                Kund är obligatoriskt
              </p>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">Skriptnamn *</label>
            <input
              type="text"
              className="form-input"
              value={flow.name || ''}
              onChange={(e) => onFlowChange({ name: e.target.value })}
              placeholder="Ange skriptnamn (obligatoriskt)"
              style={{
                fontSize: '1rem',
                borderColor: flow.name.trim() === '' ? '#ef4444' : '#d1d5db'
              }}
              required
            />
            {flow.name.trim() === '' && (
              <p style={{ fontSize: '0.75rem', color: '#ef4444', marginTop: '0.25rem' }}>
                Skriptnamn är obligatoriskt
              </p>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">Beskrivning *</label>
            <input
              type="text"
              className="form-input"
              value={flow.description || ''}
              onChange={(e) => onFlowChange({ description: e.target.value })}
              placeholder="Ange skriptbeskrivning (obligatoriskt)"
              style={{
                fontSize: '0.875rem',
                borderColor: (flow.description?.trim() || '') === '' ? '#ef4444' : '#d1d5db'
              }}
              required
            />
            {(flow.description?.trim() || '') === '' && (
              <p style={{ fontSize: '0.75rem', color: '#ef4444', marginTop: '0.25rem' }}>
                Beskrivning är obligatoriskt
              </p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div style={{
          borderTop: '1px solid #e5e7eb',
          padding: '1.5rem 0 0 0',
          marginTop: '1.5rem',
          display: 'flex',
          justifyContent: 'space-between'
        }}>
          <button
            onClick={handleCancel}
            disabled={saving}
            className="action-button secondary"
            style={{
              opacity: saving ? 0.5 : 1,
              cursor: saving ? 'not-allowed' : 'pointer'
            }}
          >
            <span>Avbryt</span>
          </button>

          <button
            onClick={handleClose}
            disabled={!canClose || saving}
            className="action-button primary"
            style={{
              opacity: (canClose && !saving) ? 1 : 0.5,
              cursor: (canClose && !saving) ? 'pointer' : 'not-allowed'
            }}
          >
            <span>{saving ? 'Sparar...' : 'Spara och fortsätt'}</span>
          </button>
        </div>
      </div>
    </Modal>
  )
}
