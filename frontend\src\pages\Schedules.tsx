import React, { useState } from 'react'
import { FlowSchedule } from '@rpa-project/shared'
import ScheduleList from '../components/ScheduleList'
import ScheduleForm from '../components/ScheduleForm'

type ViewMode = 'list' | 'create' | 'edit'

export const Schedules: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [selectedSchedule, setSelectedSchedule] = useState<FlowSchedule | null>(null)
  const [refreshKey, setRefreshKey] = useState(0)

  const handleCreateSchedule = () => {
    setSelectedSchedule(null)
    setViewMode('create')
  }

  const handleEditSchedule = (schedule: FlowSchedule) => {
    setSelectedSchedule(schedule)
    setViewMode('edit')
  }

  const handleSaveSchedule = (_schedule: FlowSchedule) => {
    setViewMode('list')
    setSelectedSchedule(null)
    setRefreshKey(prev => prev + 1) // Force refresh of schedule list
  }

  const handleCancel = () => {
    setViewMode('list')
    setSelectedSchedule(null)
  }

  const handleScheduleDelete = () => {
    setRefreshKey(prev => prev + 1) // Force refresh of schedule list
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Schedules</p>
          <p className="dashboard-subtitle">
            {viewMode === 'create' ? 'Skapa nytt schema för automatisk körning.' :
             viewMode === 'edit' ? 'Redigera schema för automatisk körning.' :
             'Hantera schemalagda körningar av dina automatiseringsflöden.'}
          </p>
        </div>

        {viewMode === 'list' && (
          <button
            onClick={handleCreateSchedule}
            className="action-button primary"
          >
            <span>Skapa schema</span>
          </button>
        )}

        {(viewMode === 'create' || viewMode === 'edit') && (
          <button
            onClick={handleCancel}
            className="action-button secondary"
          >
            <span>Tillbaka till lista</span>
          </button>
        )}
      </div>

      {viewMode === 'list' && (
        <ScheduleList
          key={refreshKey}
          onScheduleEdit={handleEditSchedule}
          onScheduleDelete={handleScheduleDelete}
        />
      )}

      {(viewMode === 'create' || viewMode === 'edit') && (
        <>
          <h2 className="section-title">
            {viewMode === 'create' ? 'Skapa schema' : 'Redigera schema'}
          </h2>
          <div className="table-container">
            <div className="activity-table">
              <ScheduleForm
                schedule={selectedSchedule || undefined}
                onSave={handleSaveSchedule}
                onCancel={handleCancel}
              />
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default Schedules
