import { Router, Request, Response } from 'express';
import { 
  RpaFlow, 
  CreateFlowRequest, 
  UpdateFlowRequest, 
  ApiResponse,
  validateFlow 
} from '@rpa-project/shared';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { FlowService } from '../services/flowService';

const router = Router();
const flowService = new FlowService();

// GET /api/flows - Get all flows with pagination
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const { limit = 20, offset = 0 } = req.query;

  const flows = await flowService.getAllFlows({
    limit: parseInt(limit as string),
    offset: parseInt(offset as string)
  });

  const response: ApiResponse<RpaFlow[]> = {
    success: true,
    data: flows
  };

  res.json(response);
}));

// GET /api/flows/:id - Get flow by ID
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const flow = await flowService.getFlowById(id);
  
  if (!flow) {
    throw createError('Flow not found', 404);
  }
  
  const response: ApiResponse<RpaFlow> = {
    success: true,
    data: flow
  };
  
  res.json(response);
}));

// POST /api/flows - Create new flow
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const createRequest: CreateFlowRequest = req.body;
  
  // Create flow object
  const flow: RpaFlow = {
    id: '', // Will be generated by service
    name: createRequest.name,
    description: createRequest.description || '',
    customerId: createRequest.customerId,
    steps: createRequest.steps,
    variables: {},
    settings: createRequest.settings || {
      browser: 'chromium',
      headless: true,
      viewport: { width: 1280, height: 720 }
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  // Create flow first (this will generate the ID)
  const createdFlow = await flowService.createFlow(flow);

  // Then validate the flow with the generated ID
  const validation = validateFlow(createdFlow);
  if (!validation.valid) {
    // If validation fails, we should clean up the created flow
    await flowService.deleteFlow(createdFlow.id);
    throw createError(`Validation failed: ${validation.errors.map(e => e.message).join(', ')}`, 400);
  }
  
  const response: ApiResponse<RpaFlow> = {
    success: true,
    data: createdFlow,
    message: 'Flow created successfully'
  };
  
  res.status(201).json(response);
}));

// PUT /api/flows/:id - Update flow
router.put('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateRequest: UpdateFlowRequest = req.body;
  
  const existingFlow = await flowService.getFlowById(id);
  if (!existingFlow) {
    throw createError('Flow not found', 404);
  }
  
  const updatedFlow: RpaFlow = {
    ...existingFlow,
    ...updateRequest,
    id, // Ensure ID doesn't change
    updatedAt: new Date()
  };
  
  // Validate updated flow
  const validation = validateFlow(updatedFlow);
  if (!validation.valid) {
    throw createError(`Validation failed: ${validation.errors.map(e => e.message).join(', ')}`, 400);
  }
  
  const result = await flowService.updateFlow(id, updatedFlow);
  
  const response: ApiResponse<RpaFlow> = {
    success: true,
    data: result,
    message: 'Flow updated successfully'
  };
  
  res.json(response);
}));

// DELETE /api/flows/:id - Delete flow
router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const existingFlow = await flowService.getFlowById(id);
  if (!existingFlow) {
    throw createError('Flow not found', 404);
  }
  
  await flowService.deleteFlow(id);
  
  const response: ApiResponse = {
    success: true,
    message: 'Flow deleted successfully'
  };
  
  res.json(response);
}));

// POST /api/flows/:id/validate - Validate flow
router.post('/:id/validate', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const flow = await flowService.getFlowById(id);
  if (!flow) {
    throw createError('Flow not found', 404);
  }
  
  const validation = validateFlow(flow);
  
  const response: ApiResponse = {
    success: validation.valid,
    data: validation,
    message: validation.valid ? 'Flow is valid' : 'Flow validation failed'
  };
  
  res.json(response);
}));

export { router as flowRoutes };
