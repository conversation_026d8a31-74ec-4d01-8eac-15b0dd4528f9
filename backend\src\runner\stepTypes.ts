/**
 * Step type categorization and runner mapping utilities
 */

/**
 * Categories of RPA step types
 */
export enum StepCategory {
  WEB_AUTOMATION = 'web_automation',
  API = 'api',
  DATABASE = 'database',
  FILE_SYSTEM = 'file_system',
  CUSTOM = 'custom'
}

/**
 * Runner types available in the system
 */
export enum RunnerType {
  PLAYWRIGHT = 'playwright',
  API = 'api',
  DATABASE = 'database',
  FILE_SYSTEM = 'file_system'
}

/**
 * Web automation step types (handled by <PERSON><PERSON><PERSON><PERSON><PERSON>)
 */
export const WEB_AUTOMATION_STEPS = [
  'navigate',
  'goBack',
  'goForward',
  'reload',
  'click',
  'fill',
  'type',
  'selectOption',
  'check',
  'uncheck',
  'waitForSelector',
  'waitForTimeout',
  'waitForUrl',
  'extractText',
  'extractAttribute',
  'takeScreenshot',
  'ifElementExists',
  'conditionalClick',
  'fillPassword',
  'fill2FA',
  'downloadFile'
] as const;

/**
 * API step types (for future API runner)
 */
export const API_STEPS = [
  'httpGet',
  'httpPost',
  'httpPut',
  'httpDelete',
  'httpPatch',
  'restCall',
  'graphqlQuery',
  'soapCall'
] as const;

/**
 * Database step types (for future database runner)
 */
export const DATABASE_STEPS = [
  'sqlQuery',
  'sqlInsert',
  'sqlUpdate',
  'sqlDelete',
  'storedProcedure',
  'bulkInsert'
] as const;

/**
 * File system step types (for future file system runner)
 */
export const FILE_SYSTEM_STEPS = [
  'readFile',
  'writeFile',
  'copyFile',
  'moveFile',
  'deleteFile',
  'createDirectory',
  'listDirectory',
  'zipFiles',
  'unzipFiles'
] as const;

/**
 * All step types
 */
export type StepType = 
  | typeof WEB_AUTOMATION_STEPS[number]
  | typeof API_STEPS[number]
  | typeof DATABASE_STEPS[number]
  | typeof FILE_SYSTEM_STEPS[number];

/**
 * Mapping from step types to runner types
 */
export const STEP_TO_RUNNER_MAP: Record<string, RunnerType> = {
  // Web automation steps
  ...Object.fromEntries(WEB_AUTOMATION_STEPS.map(step => [step, RunnerType.PLAYWRIGHT])),
  
  // API steps (for future implementation)
  ...Object.fromEntries(API_STEPS.map(step => [step, RunnerType.API])),
  
  // Database steps (for future implementation)
  ...Object.fromEntries(DATABASE_STEPS.map(step => [step, RunnerType.DATABASE])),
  
  // File system steps (for future implementation)
  ...Object.fromEntries(FILE_SYSTEM_STEPS.map(step => [step, RunnerType.FILE_SYSTEM]))
};

/**
 * Mapping from step types to categories
 */
export const STEP_TO_CATEGORY_MAP: Record<string, StepCategory> = {
  ...Object.fromEntries(WEB_AUTOMATION_STEPS.map(step => [step, StepCategory.WEB_AUTOMATION])),
  ...Object.fromEntries(API_STEPS.map(step => [step, StepCategory.API])),
  ...Object.fromEntries(DATABASE_STEPS.map(step => [step, StepCategory.DATABASE])),
  ...Object.fromEntries(FILE_SYSTEM_STEPS.map(step => [step, StepCategory.FILE_SYSTEM]))
};

/**
 * Get the runner type for a given step type
 */
export function getRunnerTypeForStep(stepType: string): RunnerType {
  const runnerType = STEP_TO_RUNNER_MAP[stepType];
  if (!runnerType) {
    throw new Error(`Unknown step type: ${stepType}`);
  }
  return runnerType;
}

/**
 * Get the category for a given step type
 */
export function getCategoryForStep(stepType: string): StepCategory {
  const category = STEP_TO_CATEGORY_MAP[stepType];
  if (!category) {
    return StepCategory.CUSTOM;
  }
  return category;
}

/**
 * Check if a step type is supported by the system
 */
export function isStepTypeSupported(stepType: string): boolean {
  return stepType in STEP_TO_RUNNER_MAP;
}

/**
 * Get all step types for a given runner type
 */
export function getStepTypesForRunner(runnerType: RunnerType): string[] {
  return Object.entries(STEP_TO_RUNNER_MAP)
    .filter(([, runner]) => runner === runnerType)
    .map(([stepType]) => stepType);
}

/**
 * Get all step types for a given category
 */
export function getStepTypesForCategory(category: StepCategory): string[] {
  return Object.entries(STEP_TO_CATEGORY_MAP)
    .filter(([, cat]) => cat === category)
    .map(([stepType]) => stepType);
}

/**
 * Check if a runner type is currently implemented
 */
export function isRunnerImplemented(runnerType: RunnerType): boolean {
  // Currently only PlaywrightRunner is implemented
  return runnerType === RunnerType.PLAYWRIGHT;
}

/**
 * Get all implemented runner types
 */
export function getImplementedRunnerTypes(): RunnerType[] {
  return [RunnerType.PLAYWRIGHT];
}

/**
 * Get all available runner types (including future ones)
 */
export function getAllRunnerTypes(): RunnerType[] {
  return Object.values(RunnerType);
}
