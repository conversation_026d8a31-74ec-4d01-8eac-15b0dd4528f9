import { 
  Credential, 
  PasswordCredential, 
  TwoFactorCredential,
  CreatePasswordCredentialRequest,
  CreateTwoFactorCredentialRequest,
  UpdatePasswordCredentialRequest,
  UpdateTwoFactorCredentialRequest,
  generateId 
} from '@rpa-project/shared';
import { statements } from '../database/database';
import { encrypt, decrypt, generateTOTP, validateBase32Secret } from '../utils/encryption';

// SQLite-based credential storage
class CredentialStorage {
  async save(credential: Credential, encryptedPassword?: string, encryptedSecret?: string): Promise<Credential> {
    const credentialData = {
      id: credential.id,
      name: credential.name,
      description: credential.description || '',
      type: credential.type,
      username: credential.type === 'password' ? (credential as PasswordCredential).username : null,
      encrypted_password: encryptedPassword || null,
      encrypted_secret: encryptedSecret || null,
      created_at: credential.createdAt.toISOString(),
      updated_at: credential.updatedAt.toISOString()
    };

    statements.insertCredential.run(
      credentialData.id,
      credentialData.name,
      credentialData.description,
      credentialData.type,
      credentialData.username,
      credentialData.encrypted_password,
      credentialData.encrypted_secret,
      credentialData.created_at,
      credentialData.updated_at
    );

    return credential;
  }

  async update(id: string, updates: Partial<Credential>, encryptedPassword?: string, encryptedSecret?: string): Promise<Credential> {
    const existing = await this.findById(id);
    if (!existing) {
      throw new Error(`Credential with id ${id} not found`);
    }

    const updatedCredential = {
      ...existing,
      ...updates,
      updatedAt: new Date()
    } as Credential;

    const credentialData = {
      name: updatedCredential.name,
      description: updatedCredential.description || '',
      username: updatedCredential.type === 'password' ? (updatedCredential as PasswordCredential).username : null,
      encrypted_password: encryptedPassword !== undefined ? encryptedPassword : null,
      encrypted_secret: encryptedSecret !== undefined ? encryptedSecret : null,
      updated_at: updatedCredential.updatedAt.toISOString()
    };

    statements.updateCredential.run(
      credentialData.name,
      credentialData.description,
      credentialData.username,
      credentialData.encrypted_password,
      credentialData.encrypted_secret,
      credentialData.updated_at,
      id
    );

    return updatedCredential;
  }

  async findById(id: string): Promise<Credential | null> {
    const row = statements.getCredentialById.get(id) as any;
    if (!row) return null;

    return this.mapRowToCredential(row);
  }

  async findAll(): Promise<Credential[]> {
    const rows = statements.getAllCredentials.all() as any[];
    return rows.map(row => this.mapRowToCredential(row));
  }

  async findByType(type: 'password' | '2fa'): Promise<Credential[]> {
    const rows = statements.getCredentialsByType.all(type) as any[];
    return rows.map(row => this.mapRowToCredential(row));
  }

  async delete(id: string): Promise<boolean> {
    const result = statements.deleteCredential.run(id);
    return result.changes > 0;
  }

  async exists(id: string): Promise<boolean> {
    const result = statements.credentialExists.get(id);
    return !!result;
  }

  private mapRowToCredential(row: any): Credential {
    const base = {
      id: row.id,
      name: row.name,
      description: row.description,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };

    if (row.type === 'password') {
      return {
        ...base,
        type: 'password',
        username: row.username
      } as PasswordCredential;
    } else {
      return {
        ...base,
        type: '2fa'
      } as TwoFactorCredential;
    }
  }

  // Get encrypted data for credential operations
  async getEncryptedData(id: string): Promise<{ password?: string; secret?: string } | null> {
    const row = statements.getCredentialById.get(id) as any;
    if (!row) return null;

    return {
      password: row.encrypted_password,
      secret: row.encrypted_secret
    };
  }
}

const storage = new CredentialStorage();

export class CredentialService {
  async createPasswordCredential(request: CreatePasswordCredentialRequest): Promise<Credential> {
    const id = generateId();
    const now = new Date();

    // Encrypt the password
    const encryptedPassword = encrypt(request.password);

    const credential: PasswordCredential = {
      id,
      name: request.name,
      description: request.description,
      type: 'password',
      username: request.username,
      createdAt: now,
      updatedAt: now
    };

    return await storage.save(credential, encryptedPassword);
  }

  async createTwoFactorCredential(request: CreateTwoFactorCredentialRequest): Promise<Credential> {
    // Validate the secret
    if (!validateBase32Secret(request.secret)) {
      throw new Error('Invalid base32 secret for 2FA');
    }

    const id = generateId();
    const now = new Date();

    // Encrypt the secret
    const encryptedSecret = encrypt(request.secret);

    const credential: TwoFactorCredential = {
      id,
      name: request.name,
      description: request.description,
      type: '2fa',
      createdAt: now,
      updatedAt: now
    };

    return await storage.save(credential, undefined, encryptedSecret);
  }

  async updatePasswordCredential(id: string, request: UpdatePasswordCredentialRequest): Promise<Credential> {
    const existing = await storage.findById(id);
    if (!existing || existing.type !== 'password') {
      throw new Error(`Password credential with id ${id} not found`);
    }

    const updates: Partial<Credential> = {};
    if (request.name !== undefined) updates.name = request.name;
    if (request.description !== undefined) updates.description = request.description;

    // Handle username separately since it's specific to password credentials
    const passwordUpdates = request.username !== undefined ? { username: request.username } : {};
    const encryptedPassword = request.password ? encrypt(request.password) : undefined;

    return await storage.update(id, { ...updates, ...passwordUpdates }, encryptedPassword);
  }

  async updateTwoFactorCredential(id: string, request: UpdateTwoFactorCredentialRequest): Promise<Credential> {
    const existing = await storage.findById(id);
    if (!existing || existing.type !== '2fa') {
      throw new Error(`2FA credential with id ${id} not found`);
    }

    // Validate the secret if provided
    if (request.secret && !validateBase32Secret(request.secret)) {
      throw new Error('Invalid base32 secret for 2FA');
    }

    const updates: Partial<Credential> = {};
    if (request.name !== undefined) updates.name = request.name;
    if (request.description !== undefined) updates.description = request.description;

    const encryptedSecret = request.secret ? encrypt(request.secret) : undefined;

    return await storage.update(id, updates, undefined, encryptedSecret);
  }

  async getCredential(id: string): Promise<Credential | null> {
    return await storage.findById(id);
  }

  async getAllCredentials(): Promise<Credential[]> {
    return await storage.findAll();
  }

  async getCredentialsByType(type: 'password' | '2fa'): Promise<Credential[]> {
    return await storage.findByType(type);
  }

  async deleteCredential(id: string): Promise<boolean> {
    return await storage.delete(id);
  }

  // Get decrypted password for automation
  async getDecryptedPassword(id: string): Promise<{ username: string; password: string } | null> {
    const credential = await storage.findById(id);
    if (!credential || credential.type !== 'password') {
      return null;
    }

    const encryptedData = await storage.getEncryptedData(id);
    if (!encryptedData?.password) {
      return null;
    }

    const password = decrypt(encryptedData.password);
    return {
      username: (credential as PasswordCredential).username,
      password
    };
  }

  // Generate TOTP code for automation
  async generateTOTPCode(id: string): Promise<string | null> {
    const credential = await storage.findById(id);
    if (!credential || credential.type !== '2fa') {
      return null;
    }

    const encryptedData = await storage.getEncryptedData(id);
    if (!encryptedData?.secret) {
      return null;
    }

    const secret = decrypt(encryptedData.secret);
    return generateTOTP(secret);
  }
}

export const credentialService = new CredentialService();
