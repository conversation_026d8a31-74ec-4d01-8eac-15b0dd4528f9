const axios = require('axios');
const fs = require('fs');

const API_BASE = 'http://localhost:3001/api';

async function testVariableInterpolation() {
  try {
    console.log('🧪 Testing Variable Interpolation System\n');

    // 1. Check if backend is running
    console.log('1. Checking backend connection...');
    try {
      await axios.get(`${API_BASE}/health`);
      console.log('✅ Backend is running\n');
    } catch (error) {
      console.log('❌ Backend is not running. Please start the backend first.');
      return;
    }

    // 2. Load test flow
    console.log('2. Loading test flow...');
    const testFlow = JSON.parse(fs.readFileSync('./test-variable-interpolation.json', 'utf8'));
    console.log(`✅ Loaded flow: ${testFlow.name}\n`);

    // 3. Create customer for testing
    console.log('3. Creating test customer...');
    const customerResponse = await axios.post(`${API_BASE}/customers`, {
      customerNumber: 'VAR-TEST-001',
      name: 'Variable Interpolation Test Customer',
      email: '<EMAIL>'
    });
    const customerId = customerResponse.data.data.id;
    console.log(`✅ Customer created: ${customerId}\n`);

    // 4. Create flow
    console.log('4. Creating flow...');
    const flowData = {
      ...testFlow,
      customerId: customerId
    };
    const createResponse = await axios.post(`${API_BASE}/flows`, flowData);
    const flowId = createResponse.data.data.id;
    console.log(`✅ Flow created with ID: ${flowId}\n`);

    // 5. Execute the flow
    console.log('5. Executing flow with variable interpolation...');
    const executeResponse = await axios.post(`${API_BASE}/executions`, {
      flowId: flowId,
      variables: {
        testMode: true,
        executionTime: new Date().toISOString()
      }
    });
    const executionId = executeResponse.data.data.id;
    console.log(`✅ Execution started: ${executionId}\n`);

    // 6. Monitor execution
    console.log('6. Monitoring execution...');
    let execution;
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds timeout

    do {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const statusResponse = await axios.get(`${API_BASE}/executions/${executionId}`);
      execution = statusResponse.data.data;
      attempts++;
      
      console.log(`   Status: ${execution.status} (${attempts}/${maxAttempts})`);
      
      if (execution.status === 'completed' || execution.status === 'failed' || attempts >= maxAttempts) {
        break;
      }
    } while (true);

    // 7. Get execution logs
    console.log('\n7. Retrieving execution logs...');
    const logsResponse = await axios.get(`${API_BASE}/executions/${executionId}/logs`);
    const logs = logsResponse.data.data;

    console.log('\n📋 Execution Logs:');
    logs.forEach((log, index) => {
      const timestamp = new Date(log.timestamp).toLocaleTimeString();
      const level = log.level.toUpperCase().padEnd(5);
      console.log(`   ${index + 1}. [${timestamp}] ${level} ${log.message}`);
      
      // Show variable data if available
      if (log.data && Object.keys(log.data).length > 0) {
        console.log(`      📊 Data: ${JSON.stringify(log.data)}`);
      }
    });

    // 8. Show final variables
    console.log('\n8. Final execution variables:');
    if (execution.variables && Object.keys(execution.variables).length > 0) {
      console.log('✅ Variables extracted:');
      Object.entries(execution.variables).forEach(([key, value]) => {
        console.log(`   ${key}: "${value}"`);
      });
    } else {
      console.log('❌ No variables found in execution result');
    }

    // 9. Verify variable interpolation worked
    console.log('\n9. Verifying variable interpolation...');
    const interpolationLogs = logs.filter(log => 
      log.message.includes('Filled') || 
      log.message.includes('Screenshot') ||
      log.message.includes('Navigated')
    );

    let interpolationWorked = false;
    interpolationLogs.forEach(log => {
      if (log.message.includes('${') === false && 
          (log.message.includes('Titel:') || log.message.includes('Beskrivning från'))) {
        interpolationWorked = true;
        console.log(`✅ Variable interpolation detected: ${log.message}`);
      }
    });

    if (!interpolationWorked) {
      console.log('❌ Variable interpolation may not have worked properly');
    }

    // 10. Summary
    console.log('\n📊 Test Summary:');
    console.log(`   Execution Status: ${execution.status}`);
    console.log(`   Total Steps: ${logs.length}`);
    console.log(`   Variables Extracted: ${Object.keys(execution.variables || {}).length}`);
    console.log(`   Variable Interpolation: ${interpolationWorked ? 'Working' : 'Needs Investigation'}`);

    if (execution.status === 'completed' && interpolationWorked) {
      console.log('\n🎉 Variable Interpolation Test PASSED!');
    } else {
      console.log('\n⚠️  Variable Interpolation Test needs review');
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testVariableInterpolation();
